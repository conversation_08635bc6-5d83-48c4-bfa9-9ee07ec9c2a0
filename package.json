{"name": "cool-admin", "version": "7.1.0", "scripts": {"dev": "vite --host", "build": "vite build", "serve": "vite preview", "lint:prettier": "prettier --write --loglevel warn \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:eslint": "eslint \"./src/**/*.{vue,ts,tsx}\" --fix"}, "dependencies": {"@codemirror/lang-javascript": "^6.0.1", "@codemirror/theme-one-dark": "^6.0.0", "@cool-vue/crud": "^7.1.16", "@element-plus/icons-vue": "^2.3.1", "@kangc/v-md-editor": "^2.3.15", "@vueuse/core": "^10.4.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.6.7", "chardet": "^2.0.0", "core-js": "^3.32.1", "dayjs": "^1.11.10", "echarts": "^5.4.3", "element-plus": "^2.5.6", "file-saver": "^2.0.5", "highlight.js": "^11.5.1", "js-beautify": "^1.13.5", "lodash-es": "^4.17.21", "marked": "^11.1.1", "mitt": "^3.0.1", "mockjs": "^1.1.0", "monaco-editor": "0.36.0", "nprogress": "^0.2.0", "pinia": "^2.1.7", "prismjs": "^1.28.0", "quill": "^1.3.7", "socket.io-client": "^4.7.2", "store": "^2.0.12", "vditor": "^3.10.8", "vue": "^3.4.15", "vue-codemirror": "^6.0.1", "vue-draggable-plus": "^0.5.3", "vue-echarts": "^6.6.1", "vue-router": "^4.2.5", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@cool-vue/vite-plugin": "^7.1.2", "@types/file-saver": "^2.0.7", "@types/lodash-es": "^4.17.8", "@types/mockjs": "^1.0.7", "@types/node": "^20.5.6", "@types/nprogress": "^0.2.0", "@types/store": "^2.0.2", "@typescript-eslint/eslint-plugin": "^6.20.0", "@typescript-eslint/parser": "^6.4.1", "@vitejs/plugin-vue": "^5.0.3", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/compiler-sfc": "^3.4.15", "eslint": "^8.48.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.1.0", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.66.1", "terser": "^5.27.0", "typescript": "^5.2.2", "vite": "^5.0.12", "vite-plugin-compression": "^0.5.1"}}