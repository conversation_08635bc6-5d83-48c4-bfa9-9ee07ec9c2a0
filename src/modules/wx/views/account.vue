<template>
	<div class="account">
		<div class="opt">
			<el-text>{{ status }}</el-text>
			<el-divider />
			<el-button type="primary" @click="login">Login</el-button>
			<el-button type="success" @click="logout">Logout</el-button>
		</div>
		<div>
			<el-image v-if="visible" :src="url" fit="cover" />
		</div>
	</div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import { ref, onMounted, onUnmounted } from "vue";
import { useCool } from "/@/cool";

const { service } = useCool();

const status = ref("");
const visible = ref(false);
const url = ref("");

let intervalId;

const getStatus = () => {
	service.base.common.wechat
		.wechatInfo()
		.then((res: any) => {
			if (res?.wechatInfo?.id) {
				status.value = `个微：${res.wechatInfo?.name} --> 已登录`;
			} else {
				status.value = `暂无账号登录`;
			}
		})
		.catch((err: any) => {
			ElMessage.error(err.message);
		});
};

const login = () => {
	service.base.common.wechat
		.getQrCode()
		.then((res: any) => {
			if (!res.qrCode.includes("weixin.qq.com")) {
				ElMessage.success(`微信客户端还没有准备好，请5秒后重试！`);
				return;
			}

			url.value = res.qrCode;
			visible.value = true;
		})
		.catch((err: any) => {
			ElMessage.error(err.message);
		});
};

const logout = () => {
	service.base.common.wechat
		.logout()
		.then((res: any) => {
			ElMessage.success(`退出成功`);
		})
		.catch((err: any) => {
			ElMessage.error(err.message);
		});
};

onMounted(() => {
	getStatus();
	intervalId = setInterval(getStatus, 2000);
});

onUnmounted(() => {
	clearInterval(intervalId);
});
</script>

<style lang="scss" scoped>
.opt {
	margin: 10px;
}
</style>
