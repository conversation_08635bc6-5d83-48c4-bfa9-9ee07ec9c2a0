<template>
	<cl-view-group ref="ViewGroup">
		<template #left>
			<div class="container">
				<el-scrollbar class="scroll">
					<div class="left">
						<div class="search">
							<el-input
								clearable
								v-model="search"
								placeholder="Search"
								@keyup.enter.native="load"
								@clear="load"
							>
								<template #prepend>
									<el-button :icon="Search" />
								</template>
							</el-input>
						</div>
						<div
							v-for="(group, index) in groups"
							:key="index"
							class="list"
							:class="{
								'is-on': group.isactive,
								'is-del': group.status === -1
							}"
							@contextmenu.stop.prevent="onContextMenu($event, group)"
							@click="loadDetails(group)"
						>
							<div class="avatar">
								<el-badge :is-dot="group.showDot">
									<el-avatar shape="square" :size="35" :src="group.avatar" />
								</el-badge>
							</div>
							<div class="left-desc">
								<div class="topic">{{ group.topic }}</div>
								<div class="sub">{{ group.lasMsgText }}</div>
								<div class="digest" v-if="group.digest">
									<img src="/@/assets/corner-left-top.svg" />
								</div>
								<div class="dateline">
									{{ datelineFormat(group.lastMsgTime) }}
								</div>
							</div>
						</div>
					</div>
				</el-scrollbar>
			</div>
		</template>
		<template #title>
			<span class="title">{{ topic }}</span>
		</template>
		<template #right-head-right>
			<el-button class="refresh" :icon="Refresh" @click="refreshDetails"
		/></template>
		<template #right>
			<div class="group">
				<div class="messages" v-if="!expandS.open">
					<el-scrollbar ref="scrollbarRef">
						<div v-if="messages.length" class="loading-top-container">
							{{ loadingTop }}
						</div>
						<div
							class="items"
							v-for="(message, index) in messages"
							:key="index"
							:class="{
								'group-message-exam': message.exam === 1,
								'group-message-sys': message.exam !== 1
							}"
						>
							<el-row class="demo-avatar demo-basic">
								<el-col :span="2">
									<el-avatar shape="square" :size="45" :src="message.avatar"
								/></el-col>

								<el-col :span="22" class="preview">
									<div class="date">
										<strong>{{ message.wxNickname }}</strong> -
										{{ datelineFormat(dateToTimeStamp(message.createTime)) }}
									</div>
									<div
										v-if="
											message.type == MessageType.Emoticon ||
											message.type == MessageType.Image
										"
									>
										<el-image
											:style="imageStyle(message)"
											:src="formatUrl(message)"
											:zoom-rate="1.2"
											:max-scale="7"
											:min-scale="0.2"
											:preview-src-list="[formatUrl(message)]"
											fit="cover"
										/>
									</div>
									<div v-else-if="message.type == MessageType.Attachment">
										<el-link :icon="Document" @click="download(message)"
											>{{ message.filename
											}}{{
												message.attachment.slice(
													message.attachment.lastIndexOf(".")
												)
											}}</el-link
										>
									</div>
									<div v-else-if="message.type == MessageType.Audio">
										<audio controls="" preload="none">
											<source :src="formatUrl(message)" type="audio/mpeg" />
										</audio>
									</div>
									<div v-else-if="message.type == MessageType.Video">
										<video controls="" preload="none">
											<source :src="formatUrl(message)" type="video/mp4" />
										</video>
									</div>
									<v-md-preview v-else :text="message.text"></v-md-preview>
								</el-col>
							</el-row>
						</div>
					</el-scrollbar>
				</div>
				<div class="input-container" v-show="inputShow">
					<div class="toolbar">
						<el-popover :width="300" trigger="click" :visible="popoverVisible">
							<template #reference>
								<img
									@click="popoverVisible = true"
									src="/@/assets/expression.svg"
									style="width: 20px; height: 20px"
								/>
							</template>
							<template #default>
								<div class="expressions">
									<div
										class="expression"
										v-for="expression in expressions"
										:key="expression.text"
										@click="expClick(expression)"
									>
										<img width="26" height="26" :src="expression.url" />
									</div>
								</div>
							</template>
						</el-popover>

						<img
							src="/@/assets/folder.svg"
							@click.stop="attachClick"
							style="width: 20px; height: 20px; margin-left: 15px"
						/>

						<input
							type="file"
							multiple
							ref="fileInput"
							style="display: none"
							@change="handleFileChange"
						/>

						<el-icon class="btnExpand" v-if="expandS.open" @click="expandToggle"
							><ArrowUp
						/></el-icon>
						<el-icon class="btnExpand" v-else="!expandS.open" @click="expandToggle"
							><ArrowDown
						/></el-icon>
					</div>
					<el-input
						v-if="!expandS.open"
						ref="send"
						v-model="input"
						@blur="captureCursor"
						@keyup.enter.native="keyDown"
						type="textarea"
						placeholder="Type your message here...（Ctrl+Enter）"
						resize="none"
						:autosize="{ minRows: 4, maxRows: 10 }"
					>
					</el-input>
					<el-input
						v-if="expandS.open"
						ref="sendFull"
						v-model="input"
						@blur="captureCursor"
						@keyup.enter.native="keyDown"
						type="textarea"
						placeholder="Type your message here...（Ctrl+Enter）"
						resize="none"
						:rows="rows"
						:style="{ height: inputHeight }"
					>
					</el-input>
					<el-button
						class="btnSend"
						:class="{
							btnSendEnable: !disabled
						}"
						:disabled="disabled"
						:icon="Promotion"
						@click="sendMessage"
					/>
				</div>
			</div>
		</template>
	</cl-view-group>
	<el-dialog v-model="dialogFormVisible" title="编辑会话" center>
		<el-form ref="ruleFormRef" :model="form">
			<el-form-item
				label="标题："
				label-width="140px"
				prop="topic"
				:rules="[
					{
						required: true,
						message: '请输入标题',
						trigger: 'blur'
					}
				]"
			>
				<el-input v-model="form.topic" autocomplete="off" />
			</el-form-item>
			<el-form-item
				label="置顶："
				label-width="140px"
				prop="digest"
				:rules="[
					{
						required: true,
						message: '请输入标题',
						trigger: 'blur'
					}
				]"
			>
				<el-checkbox v-model="form.digest" label="" size="large" />
			</el-form-item>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="dialogFormVisible = false">取消</el-button>
				<el-button type="primary" @click="save(ruleFormRef)"> 保存 </el-button>
			</span>
		</template>
	</el-dialog>
	<cl-form ref="Form" />
</template>

<script setup lang="ts">
import { ElMessage, ElScrollbar, ElInput } from "element-plus";
import type { FormInstance } from "element-plus";
import { Promotion, ArrowUp, ArrowDown, Refresh, Search } from "@element-plus/icons-vue";
import { ref, reactive, onMounted, onBeforeUnmount, nextTick, computed } from "vue";
import { ContextMenu, useForm } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { isDev } from "/@/config";
import { useBase } from "/$/base";
const { user } = useBase();
import { io } from "socket.io-client";
import { datelineFormat, dateToTimeStamp } from "/@/cool/utils";
import _ from "lodash";
import expressions from "../js/wx_expression.json";
import { useViewGroup } from "/@/plugins/view";

const { service } = useCool();

const { ViewGroup } = useViewGroup({
	custom: true
});

let wechatInfo = {};
const scrollbarRef = ref<InstanceType<typeof ElScrollbar>>();
const search = ref("");
const send = ref<any>(null);
const sendFull = ref<any>(null);
const groups = reactive<any[]>([]);
const messages = reactive<any[]>([]);
const topic = ref<string>("聊天记录");

const dialogFormVisible = ref(false);
const ruleFormRef = ref<FormInstance>();
const form = reactive({
	id: 0,
	topic: "",
	digest: false
});

const scrollbarTop = ref(0);
const isLoadingTop = ref(false);
const topNoMore = ref(false);

const inputShow = ref<boolean>(false);
const disabled = ref<boolean>(false);
const input = ref<string>("");
const uploadFiles = reactive<any[]>([]);
const fileInput = ref(null);

const popoverVisible = ref(false);

let cursorPosition = 0;
let socket: any = undefined;

const enum MessageType {
	Unknown = 0,

	Attachment = 1, // Attach(6),
	Audio = 2, // Audio(1), Voice(34)
	Contact = 3, // ShareCard(42)
	ChatHistory = 4, // ChatHistory(19)
	Emoticon = 5, // Sticker: Emoticon(15), Emoticon(47)
	Image = 6, // Img(2), Image(3)
	Text = 7, // Text(1)
	Location = 8, // Location(48)
	MiniProgram = 9, // MiniProgram(33)
	GroupNote = 10, // GroupNote(53)
	Transfer = 11, // Transfers(2000)
	RedEnvelope = 12, // RedEnvelopes(2001)
	Recalled = 13, // Recalled(10002)
	Url = 14, // Url(5)
	Video = 15, // Video(4), Video(43)
	Post = 16 // Moment, Channel, Tweet, etc
}

const expandS = reactive({
	open: false
});

const expandToggle = () => {
	expandS.open = !expandS.open;
};

const keyDown = (e: any) => {
	if (e.ctrlKey && e.keyCode == 13) {
		sendMessage();
	}
};

const rows = ref(4);
const inputHeight = ref<any>(null);

const attachClick = () => {
	fileInput.value?.click();
};

const handleFileChange = async (event: any) => {
	const files = event.target.files;
	if (files) {
		await upload(files);
	}

	event.target.value = "";
};

const upload = async (files: any) => {
	const formData = new FormData();

	for (let i = 0; i < files.length; i++) {
		formData.append("files", files[i]);
	}

	const group = groups.find((el) => el.isactive);
	formData.append("roomId", group.roomId);

	const url = isDev
		? "http://localhost:9000/dev/admin/base/open/upload4Wechat"
		: "https://connect.chasedream.com/api/v2/admin/base/open/upload4Wechat";
	const response = await fetch(url, {
		method: "POST",
		body: formData
	});
	if (response.ok) {
		const res = await response.json();
		if (res.code !== 1000) ElMessage.error(res.message);
		else {
			res.data.urls?.forEach((el: any) => {
				uploadFiles.push(el);
			});
		}
	} else {
		ElMessage.error("Upload failed");
	}
};

const captureCursor = (event: any) => {
	cursorPosition = event.target.selectionStart;
};

const insertAtCursor = (text: any) => {
	const currentInput = input.value;
	input.value = currentInput.slice(0, cursorPosition) + text + currentInput.slice(cursorPosition);
};

const toggle = (state: boolean) => {
	disabled.value = state;
};

const imageStyle = (message: any) => {
	return message.height <= 200 ? "width: auto; height: auto;" : "width: 100px; height: 100px;";
};

const closePopoverOnClickOutside = (event: any) => {
	const popoverElement = document.querySelector(".el-popover");
	if (!popoverElement.contains(event.target)) {
		popoverVisible.value = false;
	}
};

const expClick = (exp: any) => {
	popoverVisible.value = false;
	insertAtCursor(exp.text);
};

const loadingTop = computed(() => {
	if (isLoadingTop.value) {
		return "玩命加载中...";
	} else if (topNoMore.value) {
		return "已经到顶了";
	} else {
		return "";
	}
});

const formatUrl = (message: any) => {
	return isDev
		? `http://localhost:8080${message.attachment}`
		: `https://static.chasedream.com${message.attachment}`;
};

const download = (message: any) => {
	const url = isDev
		? `http://localhost:8080${message.attachment}`
		: `https://static.chasedream.com${message.attachment}`;

	const link = document.createElement("a");
	link.href = url;
	link.target = "_blank";

	link.download = `${message.attachment}${message.attachment.slice(
		message.attachment.lastIndexOf(".")
	)}`;

	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link);

	URL.revokeObjectURL(link.href);
};

const sendMessage = () => {
	if (input.value.trim() === "" || disabled.value) return;

	toggle(true);

	const group = groups.find((el) => el.isactive);

	socket.emit("message", {
		text: input.value,
		group
	});

	input.value = "";
	uploadFiles.length = 0;

	toggle(false);
};

const load = () => {
	isLoadingTop.value = false;
	topNoMore.value = false;

	service.base.common.wechat
		.list({
			topic: search.value || ""
		})
		.then((res: any) => {
			groups.length = 0;

			wechatInfo = res.wechatInfo;
			res.group.forEach((el: any) => {
				el.isactive = false;
				el.showDot = false;
				el.topic = el.topic || el.wxNickname;
				groups.push(el);
			});
		})
		.catch((err: any) => {
			ElMessage.error(err.message);
		})
		.finally(() => {});
};

const refreshDetails = () => {
	const group = groups.find((el) => el.isactive);
	if (group) loadDetails(group, 1);

	ElMessage.success(`已刷新`);
};

const loadDetails = (group: any, type = 0) => {
	let id = 0;

	groups.forEach((el) => {
		el.isactive = false;
		el.showDot = false;
	});
	group.isactive = true;

	if (type === 1 && messages.length) {
		const message = _.last(messages);
		id = message.id;
	} else if (type === 2 && messages.length) {
		const message = _.first(messages);
		id = message.id;
	}

	service.base.common.wechat
		.messages({
			groupId: group.id,
			type,
			id
		})
		.then(async (res: any) => {
			if (type === 0) {
				messages.length = 0;
				topic.value = group.topic;

				res.sort((a: any, b: any) => a.id - b.id);

				res.forEach((el: any) => {
					el.createTime = el.createTime.replace(/-/g, ".");
					messages.push(el);
				});

				topNoMore.value = false;
				scrollToBottom();
			} else if (type === 1) {
				res.sort((a: any, b: any) => a.id - b.id);

				res.forEach((el: any) => {
					el.createTime = el.createTime.replace(/-/g, ".");
					messages.push(el);
				});
			} else if (type === 2) {
				let currentScrollTop = scrollbarRef.value?.wrapRef?.scrollTop;
				const oldHeight = scrollbarRef.value?.wrapRef?.scrollHeight;

				res.forEach((el: any) => {
					el.createTime = el.createTime.replace(/-/g, ".");
					messages.unshift(el);
				});

				await nextTick();
				const newHeight = scrollbarRef.value?.wrapRef?.scrollHeight;
				scrollbarRef.value?.setScrollTop(currentScrollTop + (newHeight - oldHeight));

				topNoMore.value = res.length === 0;
			}

			inputShow.value = true;
		})
		.catch((err: any) => {
			ElMessage.error(err.message);
		})
		.finally(() => {});
};

const scrollToBottom = () => {
	nextTick(() => {
		if (scrollbarRef.value) {
			const scrollContainer = scrollbarRef.value.wrapRef;
			if (scrollContainer) {
				const maxScrollTop = scrollContainer.scrollHeight - scrollContainer.clientHeight;
				scrollbarRef.value.setScrollTop(maxScrollTop);
			}
		}
	});
};

const Form = useForm();

const save = (formEl: FormInstance | undefined) => {
	if (!formEl) return;

	formEl.validate((valid) => {
		if (valid) {
			service.base.common.wechat
				.updateGroup({
					id: form.id,
					topic: form.topic,
					digest: form.digest
				})
				.then(() => {
					ElMessage.success(`修改成功`);
					topic.value = form.topic;
					load();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				})
				.finally(() => {
					dialogFormVisible.value = false;
				});
		} else {
			console.log("error submit!");
			return false;
		}
	});
};

function rowEdit(e: any) {
	dialogFormVisible.value = true;

	form.id = e.id;
	form.topic = e.topic;
	form.digest = e.digest === 1 ? true : false;
}

function onContextMenu(e: any, { id, topic, digest }: any) {
	if (!id) {
		return false;
	}

	ContextMenu.open(e, {
		list: [
			{
				label: "编辑",
				callback(done) {
					rowEdit({ id, topic, digest });
					done();
				}
			}
		]
	});
}

const debounceLoadMoreTop = _.debounce(() => {
	const group = groups.find((el) => el.isactive);
	if (group) loadDetails(group, 2);
}, 300);

const handleScrollEvent = (event: any) => {
	const { scrollTop } = event.target;

	const isTop =
		scrollTop < 20 && scrollTop < scrollbarTop.value && !isLoadingTop.value && !topNoMore.value;

	if (isTop) {
		debounceLoadMoreTop();
	}

	scrollbarTop.value = scrollTop;
};

const wsSetup = () => {
	const url = isDev ? "http://127.0.0.1:8002" : "https://connect.chasedream.com";

	socket = io(`${url}/wx/wechat`, {
		reconnectionDelayMax: 10000,
		auth: {
			token: user.token
		},
		transports: ["websocket"]
	});

	socket.on("sys", (msg: any) => {
		console.log("ws服务端消息", msg);
	});

	socket.on("conversationErr", (msg: any) => {
		console.log("conversationErr:", msg);

		toggle(false);

		scrollToBottom();
	});

	socket.on("newWechatMessage", (res: any) => {
		console.log(res);

		groups.forEach((el) => {
			if (el.id === res.groupId && !el.isactive) el.showDot = true;
			else if (el.id === res.groupId && el.isactive) {
				messages.push(res);
				scrollToBottom();
			}
		});
	});

	socket.on("connect_error", () => {
		setTimeout(() => {
			socket.connect();
		}, 3000);
	});

	socket.on("connect", () => {
		console.log(`connect: ${socket.id}`);
	});

	socket.on("disconnect", () => {
		console.log(`disconnect`);
	});
};

const initInputFull = () => {
	const input = send.value.$el.querySelector("textarea");
	const container = input.parentElement.parentElement.parentElement;
	const containerH = container.getBoundingClientRect().height;

	const lineHeight = parseInt(getComputedStyle(input).lineHeight);
	const maxRows = containerH / lineHeight;

	rows.value = maxRows - 1;
	inputHeight.value = `${rows.value * lineHeight - 20}px`;
};

onMounted(() => {
	initInputFull();
	load();

	if (scrollbarRef.value) {
		const wrapper: any = scrollbarRef.value.wrapRef;
		wrapper.addEventListener("scroll", handleScrollEvent);
	}

	wsSetup();

	document.addEventListener("click", closePopoverOnClickOutside, true);
});

onBeforeUnmount(() => {
	if (scrollbarRef.value) {
		const wrapper: any = scrollbarRef.value.wrapRef;
		wrapper.removeEventListener("scroll", handleScrollEvent);
	}

	document.removeEventListener("click", closePopoverOnClickOutside, true);
});
</script>

<style lang="scss" scoped>
@import "../css/wechat.scss";
</style>
