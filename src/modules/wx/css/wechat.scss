.container {
	display: flex;
	flex-direction: column;
	height: 100%;
	.scroll {
		flex: 1;
	}
}

.group {
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;
	.messages {
		position: relative;
		flex: 1;
		display: flex;
		flex-direction: column;
		overflow-y: auto;
		padding: 10px 10px 0 10px;
		.preview {
			margin-top: 2px;
		}
		.date{
			font-size: 12px;
			color: #909399;
			margin-bottom: 8px;
		}
		.items {
			margin-bottom: 10px;
			padding: 10px;
			border-radius: 10px;
		}
		.loading-top-container {
			position: relative;
			top: 0;
			height: 50px;
			display: flex;
			justify-content: center;
			align-items: center;
			color:#909399;
			font-size: 12px;
		}
		.group-message-exam {
			background-color: #e2f9e2;
		}
		
		.group-message-sys {
			background-color: #F7F7F8;
		}
	}
}

:deep(.github-markdown-body) {
	padding: 0;
}

.refresh{
	border: none;
	:deep(.el-icon) {
		font-size: 20px;
	}
}

.btnSend {
	position: absolute;
	bottom: 13px;
	right: 12px;
	width: 40px;
	height: 40px;
	border: none;
	:deep(.el-icon) {
		font-size: 25px;
	}
}

.left {
	cursor: pointer;
	.search {
		margin: 8px 5px 8px 0;
	}
	.list {
		position: relative;
		display: flex;
		font-size: 14px;
		height: 50px;
		padding: 5px 15px 5px 0;
		&.is-active {
			background-color: var(--color-primary);
			color: #fff;
		}
		&:not(.is-active):hover {
			background-color: var(--el-fill-color-light);
		}
		&.is-on {
			background-color: var(--el-fill-color-light);
		}
		&.is-del {
			background-color: #E6E8EB;
		}
		.avatar {
			width: 35px;
			margin-top: 2px;
			margin-right: 5px;
		}
		.left-desc{
			display: flex;
			flex-direction: column;
			.topic{
				width: 220px;
				padding-bottom: 5px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
			.sub{
				width: 250px;
				font-size: 12px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				color:#909399;
			}
			.digest{
				position: absolute;
				left: 0px;
				top: -3px;
				width: 10px;
				height: 10px;
			}
			.dateline{
				position: absolute;
				color:#909399;
				font-size: 10px;
				right: 10px;
				top: 7px;
			}
		}
	}
}

.input-container {
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	position: relative;
	padding: 0 10px 10px 10px;
	.toolbar {
		display: flex;
		align-items: center;
		width: 100%;
		height: 40px;
		.btnAttach{
			position: absolute;
			width: 40px;
			height: 40px;
			border: none;
			:deep(.el-icon) {
				font-size: 25px;
			}
		}
		.btnExpand {
			position: absolute;
			right: 12px;
			width: 40px;
			height: 40px;
			border: none;
			:deep(.el-icon) {
				font-size: 25px;
			}
		}
		img, i {
			cursor: pointer;
		}
	}
	.btnSendEnable {
		color:#4165D7
	}
}

.expressions {
	display: flex;
	flex-wrap: wrap;
	.expression {
		margin: 4px;
	}
}
