import VueECharts from "vue-echarts";
import ElementPlus from "element-plus";
import { useStore } from "./store";
import { config } from "/@/config";
import type { ModuleConfig } from "/@/cool";
import "element-plus/theme-chalk/src/index.scss";
import "./static/css/index.scss";
import VMdEditor from "@kangc/v-md-editor";
import "@kangc/v-md-editor/lib/style/base-editor.css";
import vuepressTheme from "@kangc/v-md-editor/lib/theme/vuepress.js";
import Prism from "prismjs";
import "prismjs/components/prism-json";
import "@kangc/v-md-editor/lib/theme/style/vuepress.css";

import VMdPreview from "@kangc/v-md-editor/lib/preview";
import hljs from "highlight.js";
import "@kangc/v-md-editor/lib/style/preview.css";
import githubTheme from "@kangc/v-md-editor/lib/theme/github.js";
import "@kangc/v-md-editor/lib/theme/style/github.css";

import "/@/assets/iconfont/iconfont.css";
import "/@/assets/iconfont/iconfont.js";

export default (): ModuleConfig => {
	return {
		order: 99,
		components: Object.values(import.meta.glob("./components/**/*.{vue,tsx}")),
		views: [
			{
				path: "/console/my/info",
				meta: {
					label: "个人中心"
				},
				component: () => import("./views/info.vue")
			}
		],
		pages: [
			{
				path: "/401",
				meta: {
					process: false
				},
				component: () => import("./pages/error/401.vue")
			},
			{
				path: "/403",
				meta: {
					process: false
				},
				component: () => import("./pages/error/403.vue")
			},
			{
				path: "/404",
				meta: {
					process: false
				},
				component: () => import("./pages/error/404.vue")
			},
			{
				path: "/500",
				meta: {
					process: false
				},
				component: () => import("./pages/error/500.vue")
			},
			{
				path: "/502",
				meta: {
					process: false
				},
				component: () => import("./pages/error/502.vue")
			}
		],
		install(app) {
			// element-plus
			app.use(ElementPlus);

			// charts
			app.component("v-chart", VueECharts);

			// markdown
			VMdEditor.use(githubTheme, {
				Prism
			});
			app.use(VMdEditor);

			VMdPreview.use(githubTheme, {
				Hljs: hljs
			});
			app.use(VMdPreview);

			// 设置标题
			document.title = config.app.name;
		},
		async onLoad() {
			const { user, menu, app } = useStore();

			// token 事件
			async function hasToken(cb: () => Promise<any> | void) {
				if (cb) {
					app.addEvent("hasToken", cb);

					if (user.token) {
						await cb();
					} else {
						await user.checkForumLogin();
						await cb();
					}
				}
			}

			await hasToken(async () => {
				// 获取用户信息
				user.get();
				// 获取菜单权限
				await menu.get();
			});

			return {
				hasToken
			};
		}
	};
};
