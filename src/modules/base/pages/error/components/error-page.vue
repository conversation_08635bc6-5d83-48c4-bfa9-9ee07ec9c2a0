<template>
	<div class="error-page">
		<h1 class="code">{{ code }}</h1>
		<p class="desc">{{ desc }}</p>
	</div>
</template>

<script lang="ts" setup>
defineProps({
	code: Number,
	desc: String
});
</script>

<style lang="scss" scoped>
.error-page {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	height: 100%;
	overflow-y: auto;

	.code {
		font-size: 120px;
		font-weight: normal;
		color: #6c757d;
		font-family: "Segoe UI";
	}

	.desc {
		font-size: 16px;
		font-weight: 400;
		color: #34395e;
		margin-top: 30px;
	}

	.router {
		display: flex;
		justify-content: center;
		margin-top: 50px;
		max-width: 450px;
		width: 90%;

		.el-select {
			font-size: 14px;
			flex: 1;
		}

		.el-button {
			margin-left: 15px;
			padding: 0 30px;
		}
	}

	.link {
		display: flex;
		margin-top: 40px;

		li {
			font-weight: 500;
			cursor: pointer;
			font-size: 14px;
			margin: 0 20px;
			list-style: none;

			&:hover {
				color: var(--color-primary);
			}
		}
	}

	.copyright {
		color: #6c757d;
		font-size: 14px;
		position: fixed;
		bottom: 0;
		left: 0;
		height: 50px;
		line-height: 50px;
		width: 100%;
		text-align: center;
	}
}
</style>
