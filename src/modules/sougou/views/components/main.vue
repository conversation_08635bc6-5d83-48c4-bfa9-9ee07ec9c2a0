<template>
	<div class="view-sougou">
		<div class="wrapper" v-if="!isEdit">
			<div class="left">
				<div class="search">
					<el-input
						v-model="input"
						placeholder="关键字"
						@change="change"
						@clear="clear"
						clearable
					>
						<template #prepend> <el-button :icon="Search" /> </template
					></el-input>
				</div>
				<el-scrollbar
					ref="leftScrollbarRef"
					always
					v-if="leftList?.length !== 0 && !isEdit"
				>
					<div
						class="text item"
						v-for="el in leftList"
						:key="el.id"
						@click="nav(el.id)"
						:class="[el.active ? 'active' : '']"
					>
						<v-md-preview :text="el.highlight"></v-md-preview>
					</div>
				</el-scrollbar>
				<el-pagination
					class="pagination"
					:hide-on-single-page="true"
					background
					:currentPage="currentPage"
					:page-size="pageSize"
					layout="total, prev, pager, next"
					:total="total"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				/>
				<el-empty :image-size="200" v-if="leftList?.length === 0 && !isEdit" />
			</div>
			<div class="right">
				<el-scrollbar ref="rightScrollbarRef" always>
					<div
						:ref="setRefs(`${el.id}-item`)"
						class="text item wrap"
						:class="[el.id === selectedItem ? 'current' : '']"
						v-for="el in rightList"
						:key="el.id"
						@contextmenu.stop.prevent="open($event, el)"
					>
						<v-md-preview :text="el.content"></v-md-preview>
					</div>
					<v-md-preview></v-md-preview>
				</el-scrollbar>
			</div>
		</div>
		<div class="wrapper" v-if="isEdit">
			<v-md-editor
				v-model="editItem.content"
				left-toolbar="undo redo clear | h bold italic strikethrough quote | ul ol table hr | link image code | save"
				right-toolbar="close | preview sync-scroll fullscreen"
				:toolbar="toolbar"
				@save="save"
			></v-md-editor>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import { ref, reactive, onMounted } from "vue";
import { Search } from "@element-plus/icons-vue";
import { ElScrollbar } from "element-plus";
import { useCool } from "/@/cool";
import { ContextMenu } from "@cool-vue/crud";
import { useBase } from "/$/base";
import _ from "lodash";
import { highlight } from "/@/cool/utils";

const { refs, setRefs, route, service } = useCool();

const { app } = useBase();

const leftScrollbarRef = ref<InstanceType<typeof ElScrollbar>>();
const rightScrollbarRef = ref<InstanceType<typeof ElScrollbar>>();

const isEdit = ref(false);
const editItem = reactive({
	id: 0,
	content: ""
});

const input = ref("");
const total = ref(0);
const selectedItem = ref(0);
const leftList = ref<any[]>();
const rightList = ref<any[]>();
const currentPage = ref(1);
const pageSize = ref(50);

const type = ref(_.last(route.path.split("/")));

onMounted(() => {
	app.fold(true);
});

const toolbar = {
	close: {
		icon: "cd-icon-close",
		title: "退出",
		action() {
			isEdit.value = false;
		}
	}
};

const save = (el: any) => {
	service.base.common.sougou
		.update({
			id: editItem.id,
			content: el
		})
		.then((res) => {
			isEdit.value = false;
			nav(editItem.id);
			ElMessage.success("保存成功");
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const open = (e: any, el: any) => {
	ContextMenu.open(e, {
		list: [
			{
				label: "编辑",
				callback(done) {
					editItem.id = el.id;
					editItem.content = el.raw;
					isEdit.value = true;

					done();
				}
			}
		]
	});
};

const change = () => {
	refresh();
};

const clear = () => {
	leftList.value = [];
	rightList.value = [];
};

const nav = (id: number) => {
	rightList.value = [];
	selectedItem.value = id;

	leftList.value?.map((el) => {
		el.active = el.id === id;
	});

	more(id);
	setTimeout(() => {
		const item = refs[`${id}-item`];
		const container = item.parentNode.parentNode.getBoundingClientRect();
		const rect = item.getBoundingClientRect();

		rightScrollbarRef.value?.setScrollTop(rect.top - container.top);
	}, 500);
};

const more = (id: number) => {
	service.base.common.sougou
		.more({
			id
		})
		.then((res) => {
			const _list = [];
			const ins = [input.value, ...input.value.split("")];
			for (const hit of res?.hits?.hits) {
				_list.push({
					id: hit?._id,
					content: highlight(
						ins.join(" "),
						hit?._source?.content?.replace(
							/!\[[^\n\]]+([^\]]*)\]\(data:image/,
							"![](data:image"
						)
					),
					raw: hit?._source?.content?.replace(
						/!\[[^\n\]]+([^\]]*)\]\(data:image/,
						"![](data:image"
					)
				});
			}

			_list.sort((a, b) => a.id - b.id);
			rightList.value = _list;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const refresh = () => {
	service.base.common.sougou
		.search({
			s: input.value,
			from: currentPage.value,
			size: pageSize.value,
			type: type.value
		})
		.then((res) => {
			total.value = res?.hits?.total?.value;

			const _list = [];
			for (const hit of res?.hits?.hits) {
				_list.push({
					id: hit?._id,
					highlight: hit?.highlight?.content?.join("\n"),
					content: hit?._source?.content,
					active: false
				});
			}

			leftList.value = _list;

			leftScrollbarRef.value?.setScrollTop(0);
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};
</script>

<style lang="scss" scoped>
@import "./main.scss";
</style>
