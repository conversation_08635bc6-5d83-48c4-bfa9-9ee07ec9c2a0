.view-sougou {
	display: flex;
	background-color: #fff;
	padding: 12px;
	width: 100%;
	height: 100%;
	box-sizing: border-box;
}

.wrapper {
	margin: 10px 5px 10px 5px;
	display: flex;
	width: 100%;
	height: 100%;
	.text {
		font-size: 14px;
	}
	.item {
		margin-bottom: 10px;
	}
	.left {
		display: flex;
		flex-direction: column;
		width: 40%;
		.text {
			cursor: pointer;
			background-color: #f2f5f9;
		}
		.active {
			background-color: #e0e4f8;
		}
		.search {
			width: 100%;
			height: 32px;
		}
		.el-scrollbar {
			margin-top: 5px;
			margin-bottom: 5px;
		}
		.pagination {
			height: 50px;
		}
		.el-empty {
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
	.right {
		width: 60%;
		padding: 0 10px 5px 10px;
		.wrap p {
			word-wrap: break-word;
			word-break: break-all;
		}
		.current {
			background-color: #f2f5f9;
		}
	}
	:deep(.highlight) {
		color: #ea4335;
	}
}

:deep(.github-markdown-body) {
	padding: 16px;
}