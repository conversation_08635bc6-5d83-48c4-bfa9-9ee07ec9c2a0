<template>
	<div class="view-dashboard">
		<img src="/@/assets/analysis.svg" />
		<el-descriptions title="系统信息" :column="column" border>
			<el-descriptions-item label="IP" label-class-name="my-label">
				{{ user?.info?.ip }}
			</el-descriptions-item>
			<el-descriptions-item
				v-for="(value, key) in browserInfo"
				:key="key"
				:label="key"
				label-class-name="my-label"
			>
				{{ value }}
			</el-descriptions-item>
		</el-descriptions>
	</div>
</template>

<script setup lang="ts">
import { ref, watchEffect } from "vue";
import { useBrowser } from "/@/cool";
import BrowserType from "/@/cool/utils/browser-type";
import { useBattery } from "/$/base/hooks/useBattery";
import { useBase } from "/$/base";

const { browser } = useBrowser();
const { user } = useBase();

// descriptions 列数量
const column = ref<number>(3);
// 获取浏览器信息
const browserInfo = ref(BrowserType("zh-cn"));
// 获取电池信息
const { battery, batteryStatus, calcDischargingTime } = useBattery();

watchEffect(() => {
	Object.assign(browserInfo.value, {
		距离电池充满需要:
			Number.isFinite(battery.value.chargingTime) && battery.value.chargingTime != 0
				? calcDischargingTime.value
				: "未知",
		剩余可使用时间:
			Number.isFinite(battery.value.dischargingTime) && battery.value.dischargingTime != 0
				? calcDischargingTime.value
				: "未知",
		电池状态: batteryStatus.value,
		当前电量: `${battery.value.level}%`
	});

	column.value = browser.isMini ? 2 : 3;
});
</script>

<style lang="scss" scoped>
.view-dashboard {
	display: flex;
	background-color: #fff;
	padding: 12px;
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	flex-direction: column;

	img {
		min-height: 0;
		flex: 1;
	}

	:deep(.my-label) {
		font-weight: normal !important;
		background: #fafafa !important;
		height: 55px;
	}
}

.el-descriptions {
	flex: 1;
}
</style>
