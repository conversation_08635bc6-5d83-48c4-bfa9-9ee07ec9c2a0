<template>
	<div class="view-search">
		<div class="wrapper">
			<el-row class="opt">
				<el-button type="primary" @click="openDialog(1)">新增</el-button>
			</el-row>
			<el-row class="list">
				<el-table
					border
					:data="tableData"
					:max-height="tableHeight"
					:row-style="{ height: '40px' }"
					:header-cell-style="{ background: '#ebeef5', color: '#333' }"
				>
					<el-table-column fixed prop="id" label="ID" width="90" align="center">
						<template #header>
							<el-input
								v-model="search.id"
								size="small"
								placeholder="ID"
								clearable
								@change="refresh"
							/>
						</template>
					</el-table-column>
					<el-table-column prop="name" label="组名称" align="left">
						<template #header>
							<el-input
								v-model="search.name"
								size="small"
								placeholder="组名称"
								clearable
								@change="refresh"
							/>
						</template>
					</el-table-column>
					<el-table-column
						prop="dateline"
						label="创建时间"
						width="100"
						align="center"
						:formatter="(row:any) => formatter(row.dateline)"
					/>
					<el-table-column fixed="right" label="操作" width="140" align="center">
						<template #default="scope">
							<el-button link type="primary" plain @click="editHandler(scope.row)"
								>编辑</el-button
							>
							<el-button link type="danger" plain @click="deleteHandler(scope.row.id)"
								>删除</el-button
							>
						</template>
					</el-table-column>
				</el-table>
			</el-row>
			<el-row class="page">
				<el-pagination
					class="pagination"
					:hide-on-single-page="true"
					background
					:currentPage="currentPage"
					:page-size="pageSize"
					layout="total, sizes, prev, pager, next, jumper"
					:total="total"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				/>
			</el-row>
			<cl-dialog :title="title" v-model="visible">
				<el-form :model="form" :rules="rules" ref="formRef" label-width="80px" status-icon>
					<el-form-item label="组名称" prop="name">
						<el-input v-model="form.name" />
					</el-form-item>
					<el-form-item>
						<cl-flex1 />
						<el-button @click="closeDialog">取消</el-button>
						<el-button type="success" @click="submitForm(formRef)">保存</el-button>
					</el-form-item>
				</el-form>
			</cl-dialog>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { ref, reactive, watch, onMounted, nextTick, computed } from "vue";
import { useCool } from "/@/cool";
import { datelineToDate, removeEmptyFromObject } from "/@/cool/utils";

const { service } = useCool();

const search = reactive({
	id: "",
	name: ""
});

const formRef = ref<FormInstance>();
const form = reactive({
	id: 0,
	name: ""
});

const tableHeight = ref(0);
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);
const visible = ref(false);
const status = ref(0);

const title = computed(() => {
	return status.value === 1 ? "新增" : "编辑";
});

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.forum
		.searchRecommendGroupPage({
			...s,
			page: currentPage.value,
			size: pageSize.value
		})
		.then((res) => {
			tableData.value = res?.list;
			total.value = res?.pagination?.total;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

refresh();

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};

const formatter = (dateline: number) => {
	return dateline === 0 ? "" : datelineToDate(dateline);
};

const openDialog = (val: any) => {
	status.value = val;
	visible.value = true;
};

const closeDialog = () => {
	status.value = 0;
	visible.value = false;

	resetForm();
};

const resetForm = () => {
	form.id = 0;
	form.name = "";
};

const editHandler = (row: any) => {
	form.id = row.id;
	form.name = row.name;

	openDialog(2);
};

const deleteHandler = (id: number) => {
	ElMessageBox.confirm("此操作将永久删除选中数据，是否继续？", {
		title: "提示",
		confirmButtonText: "确定",
		cancelButtonText: "取消",
		type: "warning"
	})
		.then(() => {
			service.base.common.forum
				.searchRecommendDelete({
					id
				})
				.then((res) => {
					ElMessage({
						message: "删除成功",
						type: "success"
					});
					refresh();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		})
		.catch(() => {});
};

const rules = reactive<FormRules>({
	name: [{ required: true, message: "请输入组名称", trigger: "blur" }]
});

const submitForm = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate((valid, fields) => {
		if (valid) {
			if (status.value === 1) {
				service.base.common.forum
					.searchRecommendGroupAdd({
						...form
					})
					.then((res) => {
						ElMessage({
							message: "新增成功",
							type: "success"
						});
						closeDialog();
						resetForm();
						refresh();
					})
					.catch((err) => {
						ElMessage.error(err.message);
					});
			} else {
				service.base.common.forum
					.searchRecommendGroupUpdate({
						...form
					})
					.then((res) => {
						ElMessage({
							message: "修改成功",
							type: "success"
						});
						closeDialog();
						resetForm();
						refresh();
					})
					.catch((err) => {
						ElMessage.error(err.message);
					});
			}
		} else {
			console.log("error submit!", fields);
		}
	});
};

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 230;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 230;
		};
	});
});
</script>

<style lang="scss" scoped>
.view-search {
	display: flex;
	background-color: #fff;
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	.wrapper {
		margin: 10px 5px 10px 5px;
		display: flex;
		flex-direction: column;
		width: 100%;
		height: 100%;
		.opt {
			margin-bottom: 10px;
		}
		.list {
			display: flex;
			height: calc(100% - 70px);
			overflow: hidden;
			flex: 1;
		}
		.page {
			display: flex;
			height: 70px;
			padding-bottom: 10px;
			justify-content: right;
		}
	}
}
</style>
