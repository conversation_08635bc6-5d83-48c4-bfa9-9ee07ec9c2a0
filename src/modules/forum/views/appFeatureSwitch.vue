<template>
	<div class="view">
		<div class="wrapper">
			<el-row>
				<div class="switch">
					<el-row>
						<el-checkbox
							v-model="sendPost"
							label="显示发帖"
							size="large"
							@change="changed"
						/>
						<el-checkbox
							v-model="replyPost"
							label="显示回复"
							size="large"
							@change="changed"
						/>
						<el-checkbox
							v-model="message"
							label="显示消息"
							size="large"
							@change="changed"
						/>
						<el-checkbox
							v-model="sendDoing"
							label="显示发记录"
							size="large"
							@change="changed"
						/>
						<el-checkbox
							v-model="info"
							label="显示个人资料"
							size="large"
							@change="changed"
						/>
					</el-row>
					<el-row>
						<el-checkbox
							v-model="showMobile"
							label="显示手机号"
							size="large"
							@change="changed2"
						/>
					</el-row>
				</div>
			</el-row>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import { ref, onMounted } from "vue";
import { useCool } from "/@/cool";

const sendPost = ref(false);
const replyPost = ref(false);
const message = ref(false);
const sendDoing = ref(false);
const info = ref(false);

const showMobile = ref(false);

const { service } = useCool();

const changed = () => {
	service.base.common.forum
		.appFeatureSwitch({
			sendPost: sendPost.value,
			replyPost: replyPost.value,
			message: message.value,
			sendDoing: sendDoing.value,
			info: info.value
		})
		.then((res) => {
			ElMessage({
				message: "修改成功!",
				type: "success"
			});

			load();
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const changed2 = () => {
	service.base.common.forum
		.updateShowMobile({
			showMobile: showMobile.value
		})
		.then((res) => {
			ElMessage({
				message: "修改成功!",
				type: "success"
			});

			load();
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const load = () => {
	service.base.open
		.appFeatureSwitch({})
		.then((res: any) => {
			sendPost.value = res.sendPost;
			replyPost.value = res.replyPost;
			message.value = res.message;
			sendDoing.value = res.sendDoing;
			info.value = res.info;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});

	service.base.common.forum
		.getShowMobile({})
		.then((res: any) => {
			console.log(res);

			showMobile.value = res.value == 1 ? true : false;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

load();

onMounted(() => {});
</script>

<style lang="scss" scoped>
.view {
	display: flex;
	background-color: #fff;
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	.wrapper {
		margin: 10px 5px 10px 5px;
		display: flex;
		flex-direction: column;
		width: 100%;
		height: 100%;
		.switch {
			font-size: 15px;
		}
	}
}
</style>
