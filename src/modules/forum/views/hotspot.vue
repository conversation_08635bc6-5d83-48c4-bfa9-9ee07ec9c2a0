<template>
	<div class="view-search">
		<div class="wrapper">
			<el-row class="opt">
				<el-button type="primary" @click="openDialog(1)">新增</el-button>
			</el-row>
			<el-row class="list">
				<el-table
					border
					:data="tableData"
					:max-height="tableHeight"
					:row-style="{ height: '40px' }"
					:header-cell-style="{ background: '#ebeef5', color: '#333' }"
					:row-class-name="tableRowClassName"
				>
					<el-table-column fixed prop="id" label="ID" width="70" align="center">
						<template #header>
							<el-input
								v-model="search.id"
								size="small"
								placeholder="ID"
								clearable
								@change="refresh"
							/>
						</template>
					</el-table-column>
					<el-table-column
						prop="title"
						:show-overflow-tooltip="true"
						label="标题"
						align="left"
					>
						<template #header>
							<el-input
								v-model="search.title"
								size="small"
								placeholder="标题"
								clearable
								@change="refresh"
							/>
						</template>
					</el-table-column>
					<el-table-column
						prop="targetUrl"
						label="目标地址"
						:show-overflow-tooltip="true"
						width="170"
						align="left"
					>
						<template #header>
							<el-input
								v-model="search.targetUrl"
								size="small"
								placeholder="目标地址"
								clearable
								@change="refresh"
							/>
						</template>
					</el-table-column>
					<el-table-column
						prop="begin"
						label="开始时间"
						width="100"
						align="center"
						:formatter="(row:any) => formatter(row.begin)"
					/>
					<el-table-column
						prop="end"
						label="结束时间"
						width="100"
						align="center"
						:formatter="(row:any) => formatter(row.end)"
					/>
					<el-table-column
						prop="referer"
						label="Referer"
						width="110"
						:show-overflow-tooltip="true"
						align="center"
					>
					</el-table-column>
					<el-table-column
						label="次数范围"
						width="110"
						:show-overflow-tooltip="true"
						align="center"
					>
						<template #default="scope">
							{{ scope.row.showTimeMin }} - {{ scope.row.showTimeMax }}
						</template>
					</el-table-column>
					<el-table-column
						label="间隔范围(分钟)"
						width="120"
						:show-overflow-tooltip="true"
						align="center"
					>
						<template #default="scope">
							{{ scope.row.showIntervalMin }} - {{ scope.row.showIntervalMax }}
						</template>
					</el-table-column>
					<el-table-column
						prop="type"
						label="类型"
						width="60"
						align="center"
						:formatter="(row:any) => typeF(row.type)"
					/>

					<el-table-column fixed="right" label="操作" width="140" align="center">
						<template #default="scope">
							<el-button link type="primary" plain @click="editHandler(scope.row)"
								>编辑</el-button
							>
							<el-button link type="danger" plain @click="deleteHandler(scope.row.id)"
								>删除</el-button
							>
						</template>
					</el-table-column>
				</el-table>
			</el-row>
			<el-row class="page">
				<el-pagination
					class="pagination"
					:hide-on-single-page="true"
					background
					:currentPage="currentPage"
					:page-size="pageSize"
					layout="total, sizes, prev, pager, next, jumper"
					:total="total"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				/>
			</el-row>
			<cl-dialog :title="title" v-model="visible">
				<el-form :model="form" :rules="rules" ref="formRef" label-width="80px" status-icon>
					<el-form-item label="标题" prop="title">
						<el-input v-model="form.title" />
					</el-form-item>

					<el-form-item label="目标地址" prop="targetUrl">
						<el-input v-model="form.targetUrl" />
					</el-form-item>

					<el-form-item label="日期" required>
						<el-row>
							<el-col :span="11">
								<el-form-item prop="begin">
									<el-date-picker
										v-model="form.begin"
										type="datetime"
										placeholder="开始时间"
										style="width: 100%"
										value-format="X"
									/>
								</el-form-item>
							</el-col>
							<el-col style="text-align: center" :span="2">
								<span class="text-gray-500">-</span>
							</el-col>
							<el-col :span="11">
								<el-form-item prop="end">
									<el-date-picker
										v-model="form.end"
										type="datetime"
										placeholder="结束时间"
										style="width: 100%"
										value-format="X"
									/>
								</el-form-item>
							</el-col>
						</el-row>
					</el-form-item>

					<el-form-item label="次数范围" required>
						<el-row>
							<el-col :span="11">
								<el-form-item prop="showTimeMin">
									<el-input v-model="form.showTimeMin" placeholder="最小" />
								</el-form-item>
							</el-col>
							<el-col style="text-align: center" :span="2">
								<span class="text-gray-500">-</span>
							</el-col>
							<el-col :span="11">
								<el-form-item prop="showTimeMax">
									<el-input v-model="form.showTimeMax" placeholder="最大" />
								</el-form-item>
							</el-col>
						</el-row>
						<el-row>
							<el-text class="tips"
								>每日0时，随机生成当日访问次数的任务，这是随机数的范围</el-text
							>
						</el-row>
					</el-form-item>

					<el-form-item label="间隔范围" required>
						<el-row>
							<el-col :span="11">
								<el-form-item prop="showIntervalMin">
									<el-input
										v-model="form.showIntervalMin"
										placeholder="最小（分钟）"
									/>
								</el-form-item>
							</el-col>
							<el-col style="text-align: center" :span="2">
								<span class="text-gray-500">-</span>
							</el-col>
							<el-col :span="11">
								<el-form-item prop="showIntervalMax">
									<el-input
										v-model="form.showIntervalMax"
										placeholder="最大（分钟）"
									/>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row>
							<el-text class="tips">
								生成任务时，每2次访问之间的时间间隔（分钟）</el-text
							>
						</el-row>
					</el-form-item>

					<el-form-item label="Referer" prop="referer">
						<el-input v-model="form.referer" :rows="8" type="textarea" />
					</el-form-item>
					<el-form-item label="类型" prop="type">
						<el-col :span="20">
							<el-select v-model="form.type" class="m-2" placeholder="请选择类型">
								<el-option
									v-for="(item, index) in groupOptions"
									:key="index"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</el-col>
					</el-form-item>

					<el-form-item>
						<cl-flex1 />
						<el-button @click="closeDialog">取消</el-button>
						<el-button type="success" @click="submitForm(formRef)">保存</el-button>
					</el-form-item>
				</el-form>
			</cl-dialog>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { ref, reactive, watch, onMounted, nextTick, computed } from "vue";
import { useCool } from "/@/cool";
import { datelineToDate, removeEmptyFromObject } from "/@/cool/utils";

const { service } = useCool();

const search = reactive({
	id: "",
	title: "",
	targetUrl: ""
});

const formRef = ref<FormInstance>();
const form = reactive({
	id: 0,
	begin: 0,
	end: 0,
	title: "",
	targetUrl: "",
	referer: "",
	showTimeMax: "",
	showTimeMin: "",
	showIntervalMin: "",
	showIntervalMax: "",
	type: 0
});

const tableHeight = ref(0);
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);
const visible = ref(false);
const status = ref(0);
const groupOptions = ref([
	{
		label: "0 x 0",
		value: 0
	},
	{
		label: "1 x 1",
		value: 1
	},
	{
		label: "弹窗展示",
		value: 2
	}
]);

const title = computed(() => {
	return status.value === 1 ? "新增" : "编辑";
});

watch(
	() => form.end,
	(newValue: number, oldValue: number) => {
		if (!oldValue && status.value === 1) {
			form.end = Number(newValue) + 60 * 60 * 24 - 1;
		}
	}
);

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.hotspot
		.hotSpotPage({
			...s,
			page: currentPage.value,
			size: pageSize.value
		})
		.then((res) => {
			tableData.value = res?.list;
			total.value = res?.pagination?.total;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

refresh();

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};

const formatter = (dateline: number) => {
	return dateline === 0 ? "" : datelineToDate(dateline);
};

const typeF = (type: number) => {
	if (type === 0) {
		return "0 x 0";
	} else if (type === 1) {
		return "1 x 1";
	} else if (type === 2) {
		return "弹窗展示";
	}

	return "";
};

const openDialog = (val: any) => {
	status.value = val;
	visible.value = true;
};

const closeDialog = () => {
	status.value = 0;
	visible.value = false;

	resetForm();
};

const resetForm = () => {
	form.id = 0;
	form.title = "";
	form.targetUrl = "";
	form.begin = 0;
	form.end = 0;
	form.referer = "";
	form.showTimeMin = "";
	form.showTimeMax = "";
	form.showIntervalMin = "";
	form.showIntervalMax = "";
	form.type = 0;
};

const editHandler = (row: any) => {
	form.id = row.id;
	form.title = row.title;
	form.targetUrl = row.targetUrl;
	form.begin = row.begin;
	form.end = row.end;
	form.referer = row.referer;
	form.showTimeMin = row.showTimeMin;
	form.showTimeMax = row.showTimeMax;
	form.showIntervalMin = row.showIntervalMin;
	form.showIntervalMax = row.showIntervalMax;
	form.type = row.type;

	openDialog(2);
};

const deleteHandler = (id: number) => {
	ElMessageBox.confirm("此操作将永久删除选中数据，是否继续？", {
		title: "提示",
		confirmButtonText: "确定",
		cancelButtonText: "取消",
		type: "warning"
	})
		.then(() => {
			service.base.common.hotspot
				.hotspotDelete({
					id
				})
				.then((res) => {
					ElMessage({
						message: "删除成功",
						type: "success"
					});
					refresh();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		})
		.catch(() => {});
};

const rules = reactive<FormRules>({
	title: [{ required: true, message: "请输入标题", trigger: "blur" }],
	targetUrl: [{ required: true, message: "请输入目标地址", trigger: "blur" }],
	referer: [{ required: true, message: "请输入Referer", trigger: "blur" }],
	begin: [{ required: true, message: "请输入开始时间", trigger: "blur" }],
	showTimeMin: [{ required: true, message: "请输入最小展示范围", trigger: "blur" }],
	showTimeMax: [{ required: true, message: "请输入最大展示范围", trigger: "blur" }],
	showIntervalMin: [{ required: true, message: "请输入最小间隔范围", trigger: "blur" }],
	showIntervalMax: [{ required: true, message: "请输入最大间隔范围", trigger: "blur" }],
	type: [{ required: true, message: "请选择分组", trigger: "blur" }]
});

const submitForm = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate((valid, fields) => {
		if (valid) {
			if (status.value === 1) {
				service.base.common.hotspot
					.hotspotAdd({
						...form
					})
					.then((res) => {
						ElMessage({
							message: "新增成功",
							type: "success"
						});
						closeDialog();
						resetForm();
						refresh();
					})
					.catch((err) => {
						ElMessage.error(err.message);
					});
			} else {
				service.base.common.hotspot
					.hotspotUpdate({
						...form
					})
					.then((res) => {
						ElMessage({
							message: "修改成功",
							type: "success"
						});
						closeDialog();
						resetForm();
						refresh();
					})
					.catch((err) => {
						ElMessage.error(err.message);
					});
			}
		} else {
			console.log("error submit!", fields);
		}
	});
};

const tableRowClassName = (row: any) => {
	console.log(row);
	if (row.row.status === -1) {
		return 'disabled-row'
	}
	return ''
}

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 230;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 230;
		};
	});
});
</script>

<style lang="scss" scoped>
.view-search {
	display: flex;
	background-color: #fff;
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	.wrapper {
		margin: 10px 5px 10px 5px;
		display: flex;
		flex-direction: column;
		width: 100%;
		height: 100%;
		.opt {
			margin-bottom: 10px;
		}
		.list {
			display: flex;
			height: calc(100% - 70px);
			overflow: hidden;
			flex: 1;
		}
		.page {
			display: flex;
			height: 70px;
			padding-bottom: 10px;
			justify-content: right;
		}
		.tips {
			font-size: 12px;
		}
	}
}

.cursor-pointer {
	cursor: pointer;
	color: var(--color-primary);
}

:deep(.disabled-row) {
	--el-table-tr-bg-color: #F5F7FA;
}
</style>
