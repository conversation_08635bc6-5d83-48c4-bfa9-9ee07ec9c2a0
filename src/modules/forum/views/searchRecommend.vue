<template>
	<div class="view-search">
		<div class="wrapper">
			<el-row class="opt">
				<el-button type="primary" @click="openDialog(1)">新增</el-button>
			</el-row>
			<el-row class="list">
				<el-table
					border
					:data="tableData"
					:max-height="tableHeight"
					:row-style="{ height: '40px' }"
					:header-cell-style="{ background: '#ebeef5', color: '#333' }"
				>
					<el-table-column fixed prop="id" label="ID" width="70" align="center">
						<template #header>
							<el-input
								v-model="search.id"
								size="small"
								placeholder="ID"
								clearable
								@change="refresh"
							/>
						</template>
					</el-table-column>
					<el-table-column prop="gid" label="组ID" width="70" align="center">
						<template #header>
							<el-input
								v-model="search.gid"
								size="small"
								placeholder="组ID"
								clearable
								@change="refresh"
							/>
						</template>
					</el-table-column>
					<el-table-column prop="name" label="组名称" width="110" align="center">
						<template #header>
							<el-input
								v-model="search.name"
								size="small"
								placeholder="组名称"
								clearable
								@change="refresh"
							/>
						</template>
					</el-table-column>
					<el-table-column
						prop="keyword"
						label="关键字"
						:show-overflow-tooltip="true"
						align="left"
					>
						<template #header>
							<el-input
								v-model="search.keyword"
								size="small"
								placeholder="关键字"
								clearable
								@change="refresh"
							/>
						</template>
					</el-table-column>
					<el-table-column
						prop="content"
						label="HTML"
						width="200"
						:show-overflow-tooltip="true"
						align="center"
					>
						<template #header>
							<el-input
								v-model="search.content"
								size="small"
								placeholder="HTML"
								clearable
								@change="refresh"
							/>
						</template>
					</el-table-column>
					<el-table-column prop="order" label="权重" width="60" align="center" />
					<el-table-column
						prop="begin"
						label="开始时间"
						width="100"
						align="center"
						:formatter="(row:any) => formatter(row.begin)"
					/>
					<el-table-column
						prop="end"
						label="结束时间"
						width="100"
						align="center"
						:formatter="(row:any) => formatter(row.end)"
					/>
					<el-table-column label="状态" width="80" align="center">
						<template #default="scope">
							<el-switch
								v-model="scope.row.status"
								:active-value="1"
								:inactive-value="0"
								@change="switchHandler(scope.row)"
							/>
						</template>
					</el-table-column>
					<el-table-column
						prop="dateline"
						label="创建时间"
						width="100"
						align="center"
						:formatter="(row:any) => formatter(row.dateline)"
					/>
					<el-table-column fixed="right" label="操作" width="140" align="center">
						<template #default="scope">
							<el-button link type="primary" plain @click="editHandler(scope.row)"
								>编辑</el-button
							>
							<el-button link type="danger" plain @click="deleteHandler(scope.row.id)"
								>删除</el-button
							>
						</template>
					</el-table-column>
				</el-table>
			</el-row>
			<el-row class="page">
				<el-pagination
					class="pagination"
					:hide-on-single-page="true"
					background
					:currentPage="currentPage"
					:page-size="pageSize"
					layout="total, sizes, prev, pager, next, jumper"
					:total="total"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				/>
			</el-row>
			<cl-dialog :title="title" v-model="visible">
				<el-form :model="form" :rules="rules" ref="formRef" label-width="80px" status-icon>
					<el-form-item label="组名称" prop="gid">
						<el-col :span="20">
							<el-select v-model="form.gid" class="m-2" placeholder="请选择分组">
								<el-option
									v-for="item in groupOptions"
									:key="item.id"
									:label="item.name"
									:value="item.id"
								/>
							</el-select>
						</el-col>
						<el-col :span="4">
							<el-row type="flex" justify="end"
								><span class="cursor-pointer" @click="handleCopy"> 模板代码 </span>
							</el-row>
						</el-col>
					</el-form-item>
					<el-form-item label="关键字" prop="keyword">
						<el-input v-model="form.keyword" />
					</el-form-item>
					<el-form-item label="HTML" prop="content">
						<el-input v-model="form.content" :rows="8" type="textarea" />
					</el-form-item>
					<el-form-item label="日期">
						<el-col :span="11">
							<el-form-item prop="begin">
								<el-date-picker
									v-model="form.begin"
									type="datetime"
									label="开始时间"
									placeholder="开始时间"
									style="width: 100%"
									value-format="X"
								/>
							</el-form-item>
						</el-col>
						<el-col style="text-align: center" :span="2">
							<span class="text-gray-500">-</span>
						</el-col>
						<el-col :span="11">
							<el-form-item prop="end">
								<el-date-picker
									v-model="form.end"
									type="datetime"
									label="结束时间"
									placeholder="结束时间"
									style="width: 100%"
									value-format="X"
								/>
							</el-form-item>
						</el-col>
					</el-form-item>
					<el-form-item label="权重" prop="order">
						<el-input v-model="form.order" placeholder="0-255" />
					</el-form-item>
					<el-form-item>
						<cl-flex1 />
						<el-button @click="closeDialog">取消</el-button>
						<el-button type="success" @click="submitForm(formRef)">保存</el-button>
					</el-form-item>
				</el-form>
			</cl-dialog>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { ref, reactive, watch, onMounted, nextTick, computed } from "vue";
import { useCool } from "/@/cool";
import { datelineToDate, removeEmptyFromObject } from "/@/cool/utils";
import { useClipboard } from "@vueuse/core";

const { copy, isSupported } = useClipboard();

const { service } = useCool();

const search = reactive({
	id: "",
	gid: "",
	name: "",
	keyword: "",
	content: ""
});

const formRef = ref<FormInstance>();
const form = reactive({
	id: 0,
	gid: "",
	name: "",
	keyword: "",
	content: "",
	status: true,
	begin: 0,
	end: 0,
	order: 150
});

const tableHeight = ref(0);
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);
const visible = ref(false);
const status = ref(0);
const groupOptions = ref([]);

const copyContent = ref(
	'<iframe class="cdsr" src="https://ad.doubleclick.net/ddm/trackclk/********.2488703CHASEDREAMFORUM/*********.343871833;dc_trk_aid=535296842;dc_trk_cid=176172604;dc_lat=;dc_rdid=;tag_for_child_directed_treatment=;tfua=;ltd=" frameborder="0" style="width:480px;height:1000px;"></iframe>'
);

const title = computed(() => {
	return status.value === 1 ? "新增" : "编辑";
});

watch(
	() => form.end,
	(newValue: number, oldValue: number) => {
		if (!oldValue && status.value === 1) {
			form.end = Number(newValue) + 60 * 60 * 24 - 1;
		}
	}
);

const handleCopy = () => {
	copy(copyContent.value);
	ElMessage({
		message: "复制成功",
		type: "success"
	});
};

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.forum
		.searchRecommendPage({
			...s,
			page: currentPage.value,
			size: pageSize.value
		})
		.then((res) => {
			tableData.value = res?.list;
			total.value = res?.pagination?.total;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

refresh();

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};

const formatter = (dateline: number) => {
	return dateline === 0 ? "" : datelineToDate(dateline);
};

const openDialog = (val: any) => {
	status.value = val;
	visible.value = true;

	service.base.common.forum
		.searchRecommendGroupList()
		.then((res) => {
			groupOptions.value = res;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const closeDialog = () => {
	status.value = 0;
	visible.value = false;

	resetForm();
};

const resetForm = () => {
	form.id = 0;
	form.gid = "";
	form.keyword = "";
	form.content = "";
	form.status = true;
	form.begin = 0;
	form.end = 0;
	form.order = 150;
};

const editHandler = (row: any) => {
	form.id = row.id;
	form.gid = row.gid;
	form.keyword = row.keyword;
	form.content = row.content;
	form.status = row.status;
	form.begin = row.begin;
	form.end = row.end;
	form.order = row.order;

	openDialog(2);
};

const deleteHandler = (id: number) => {
	ElMessageBox.confirm("此操作将永久删除选中数据，是否继续？", {
		title: "提示",
		confirmButtonText: "确定",
		cancelButtonText: "取消",
		type: "warning"
	})
		.then(() => {
			service.base.common.forum
				.searchRecommendDelete({
					id
				})
				.then((res) => {
					ElMessage({
						message: "删除成功",
						type: "success"
					});
					refresh();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		})
		.catch(() => {});
};

const switchHandler = (row: any) => {
	service.base.common.forum
		.searchRecommendUpdate({
			id: row.id,
			status: row.status
		})
		.then((res) => {
			ElMessage({
				message: "修改成功",
				type: "success"
			});
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const rules = reactive<FormRules>({
	gid: [{ required: true, message: "请选择分组", trigger: "blur" }],
	keyword: [{ required: true, message: "请输入关键字", trigger: "blur" }],
	content: [{ required: true, message: "请输入HTML", trigger: "blur" }]
});

const submitForm = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate((valid, fields) => {
		if (valid) {
			if (status.value === 1) {
				service.base.common.forum
					.searchRecommendAdd({
						...form
					})
					.then((res) => {
						ElMessage({
							message: "新增成功",
							type: "success"
						});
						closeDialog();
						resetForm();
						refresh();
					})
					.catch((err) => {
						ElMessage.error(err.message);
					});
			} else {
				service.base.common.forum
					.searchRecommendUpdate({
						...form
					})
					.then((res) => {
						ElMessage({
							message: "修改成功",
							type: "success"
						});
						closeDialog();
						resetForm();
						refresh();
					})
					.catch((err) => {
						ElMessage.error(err.message);
					});
			}
		} else {
			console.log("error submit!", fields);
		}
	});
};

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 230;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 230;
		};
	});
});
</script>

<style lang="scss" scoped>
.view-search {
	display: flex;
	background-color: #fff;
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	.wrapper {
		margin: 10px 5px 10px 5px;
		display: flex;
		flex-direction: column;
		width: 100%;
		height: 100%;
		.opt {
			margin-bottom: 10px;
		}
		.list {
			display: flex;
			height: calc(100% - 70px);
			overflow: hidden;
			flex: 1;
		}
		.page {
			display: flex;
			height: 70px;
			padding-bottom: 10px;
			justify-content: right;
		}
	}
}

.cursor-pointer {
	cursor: pointer;
	color: var(--color-primary);
}
</style>
