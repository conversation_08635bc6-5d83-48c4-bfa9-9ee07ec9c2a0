<template>
	<div class="view-search">
		<div class="wrapper">
			<el-row>
				<el-input
					v-model="input"
					type="textarea"
					resize="none"
					:autosize="{ minRows: 4, maxRows: 10 }"
				>
				</el-input>
			</el-row>
			<el-row>
				<el-button @click="save" type="primary" class="search">保存</el-button>
			</el-row>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import { ref, onMounted } from "vue";
import { useCool } from "/@/cool";

const { service } = useCool();
const input = ref("");

const save = () => {
	service.base.common.forum
		.sensitiveMonitorUpdate({
			val: input.value
		})
		.then((res: any) => {
			ElMessage({
				message: "修改成功",
				type: "success"
			});
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const load = () => {
	service.base.common.forum
		.sensitiveMonitorGet()
		.then((res: any) => {
			input.value = res;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

load();

onMounted(() => {});
</script>

<style lang="scss" scoped>
.view-search {
	display: flex;
	background-color: #fff;
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	.wrapper {
		margin: 10px 5px 10px 5px;
		display: flex;
		flex-direction: column;
		width: 100%;
		height: 100%;
		.search {
			margin-top: 8px;
			margin-bottom: 8px;
		}
	}
}
</style>
