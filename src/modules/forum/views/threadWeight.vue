<template>
	<div class="view-search">
		<div class="wrapper">
			<el-row class="list">
				<el-table
					border
					:data="tableData"
					:max-height="tableHeight"
					:row-style="{ height: '40px' }"
					:header-cell-style="{ background: '#ebeef5', color: '#333' }"
				>
					<el-table-column fixed prop="id" label="ID" width="90" align="center">
						<template #header>
							<el-input
								v-model="search.id"
								size="small"
								placeholder="ID"
								clearable
								@change="refresh"
							/>
						</template>
					</el-table-column>
					<el-table-column prop="tid" label="TID" width="120" align="center">
						<template #header>
							<el-input
								v-model="search.tid"
								size="small"
								placeholder="TID"
								clearable
								@change="refresh"
							/>
						</template>
						<template #default="scope">
							<el-link
								type="primary"
								:underline="false"
								@click="openUrl(scope.row.tid)"
								>{{ scope.row.tid }}</el-link
							>
						</template>
					</el-table-column>
					<el-table-column prop="weight" label="权重" align="center"></el-table-column>
					<el-table-column
						prop="dateline"
						label="创建时间"
						width="100"
						align="center"
						:formatter="formatter"
					/>
				</el-table>
			</el-row>
			<el-row class="page">
				<el-pagination
					class="pagination"
					:hide-on-single-page="true"
					background
					:currentPage="currentPage"
					:page-size="pageSize"
					layout="total, sizes, prev, pager, next, jumper"
					:total="total"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				/>
			</el-row>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import { ref, reactive, onMounted, nextTick } from "vue";
import { useCool } from "/@/cool";
import { datelineToDate, removeEmptyFromObject } from "/@/cool/utils";

const { service } = useCool();

const search = reactive({
	id: "",
	tid: "",
	weight: ""
});

const tableHeight = ref(0);
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.forum
		.threadWeightPage({
			...s,
			page: currentPage.value,
			size: pageSize.value
		})
		.then((res) => {
			tableData.value = res?.list;
			total.value = res?.pagination?.total;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

refresh();

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};

const formatter = (row: any) => {
	return datelineToDate(row.dateline);
};

const openUrl = (tid: number) => {
	const url = `https://forum.chasedream.com/thread-${tid}-1-1.html`;
	window.open(url);
};

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 180;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 180;
		};
	});
});
</script>

<style lang="scss" scoped>
.view-search {
	display: flex;
	background-color: #fff;
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	.wrapper {
		margin: 10px 5px 10px 5px;
		display: flex;
		flex-direction: column;
		width: 100%;
		height: 100%;
		.list {
			display: flex;
			height: calc(100% - 70px);
			overflow: hidden;
			flex: 1;
		}
		.page {
			display: flex;
			height: 70px;
			padding-bottom: 10px;
			justify-content: right;
		}
	}
}
</style>
