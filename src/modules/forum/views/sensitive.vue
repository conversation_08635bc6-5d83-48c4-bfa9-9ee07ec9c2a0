<template>
	<div class="view-search">
		<div class="wrapper">
			<el-row>
				<el-input
					v-model="input"
					type="textarea"
					resize="none"
					@input="inputChanged"
					:autosize="{ minRows: 4, maxRows: 10 }"
				>
				</el-input>
			</el-row>
			<el-row>
				<el-button @click="search" :disabled="searchStatus" type="primary" class="search"
					>查询</el-button
				>
			</el-row>
			<el-row class="html" v-html="sensitive"> </el-row>
			
			<el-row class="pid-row">
				<el-input v-model="pid" placeholder="pid" class="pid-input"></el-input>
			</el-row>
			<el-row>
				<el-button @click="openThread" :disabled="!pid.length" type="primary" class="open-btn"
					>打开</el-button
				>
			</el-row>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import { ref, onMounted, computed } from "vue";
import { useCool } from "/@/cool";

const { service } = useCool();
const input = ref("");
const sensitive = ref("");
const pid = ref("");

const searchStatus = computed(() => input.value.length === 0);

const inputChanged = (val: any) => {
	if (val.length === 0) sensitive.value = "";
};

const search = () => {
	service.base.common.forum
		.sensitive({
			message: input.value
		})
		.then((res: any) => {
			let txt = "";

			if (res.length > 0) {
				for (const el of res) {
					txt += `${el.word} --- ${el.type} <br />`;
				}
			} else {
				txt = "暂无";
			}
			sensitive.value = txt;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const openThread = () => {
	if (!pid.value) {
		ElMessage.warning("请输入pid");
		return;
	}
	
	service.base.common.forum
		.getThreadUrlByPid({
			pid: pid.value
		})
		.then((res: any) => {						
			window.open(res.url, "_blank");
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

onMounted(() => {});
</script>

<style lang="scss" scoped>
.view-search {
	display: flex;
	background-color: #fff;
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	.wrapper {
		margin: 10px 5px 10px 5px;
		display: flex;
		flex-direction: column;
		width: 100%;
		height: 100%;
		.search {
			margin-top: 8px;
			margin-bottom: 8px;
		}
		.pid-row {
			margin-top: 20px;
			margin-bottom: 8px;
		}
		.open-btn {
			margin-bottom: 8px;
		}
		.html {
			color: #374151;
		}
	}
}
</style>
