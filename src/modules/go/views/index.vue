<template>
	<cl-crud ref="Crud">
		<el-row>
			<cl-add-btn>新增根节点</cl-add-btn>
			<cl-refresh-btn />
		</el-row>
		<el-row>
			<cl-table ref="Table" row-key="ID" @row-click="onRowClick">
				<template #column-OriginalUrl="{ scope }">
					<span>{{ scope.row.OriginalUrl }}</span>
				</template>

				<template #column-copy="{ scope }">
					<el-icon
						size="20"
						class="copy cursor-pointer"
						@click.stop="copyClick(scope.row)"
						style="margin-right: 10px"
						><document-copy
					/></el-icon>
					<el-icon size="20" class="cursor-pointer" @click.stop="searchClick(scope.row)"
						><search
					/></el-icon>
				</template>

				<template #slot-add="{ scope }">
					<el-button type="success" text bg @click.stop="append(scope.row)"
						>新增</el-button
					>
				</template>
			</cl-table>
		</el-row>

		<el-dialog
			class="dialog"
			v-model="statisticsForm.visible"
			:title="statisticsForm.title"
			width="900"
			center
		>
			<el-row class="header">
				<el-button link type="primary" :icon="Refresh" @click="searchRefresh"
					>刷新</el-button
				>
				<el-button link type="success" :icon="Tickets" @click="exportHandle(1)"
					>导出详情</el-button
				>
				<el-button link type="success" :icon="Tickets" @click="exportHandle(3)"
					>导出统计</el-button
				>
				<el-button plain size="small" @click="filter(1)">年</el-button>
				<el-button plain size="small" @click="filter(2)">月</el-button>
				<el-button plain size="small" @click="filter(3)">周</el-button>
				<el-button plain size="small" @click="filter(0)">日</el-button>
				<el-date-picker
					v-model="statisticsForm.daterange"
					type="daterange"
					unlink-panels
					range-separator="-"
					start-placeholder="开始时间"
					end-placeholder="结束时间"
					:shortcuts="shortcuts"
					size="small"
					class="daterange"
					value-format="X"
					@change="filter(4)"
				/>
				<el-button plain size="small" :icon="Search" @click="searchRefresh" />
			</el-row>
			
			<el-row class="list">				
				<el-row v-show="queryData.length" class="query-tags" style="margin-bottom: 20px;">
					<el-tag
						v-for="item in queryData"
						:key="item.name"
						:type="getTagType(item.count)"
						effect="light"
						class="query-tag"
						:size="'default'"
					>
						{{ item.name }} ({{ item.count }})
					</el-tag>
				</el-row>

				<el-table
					:data="statisticsForm.data"
					:row-style="{ height: '40px' }"
					style="width: 100%"
					height="520"
					row-key="ID"
					border
				>
					<el-table-column prop="CreateTime" label="日期">
						<template #default="scope">
							{{ formatter(scope.row) }}
						</template>
					</el-table-column>
					<el-table-column label="跳转次数">
						<template #default="scope">
							<el-button link type="primary" plain @click="hitsClick(scope.row)">{{
								scope.row.Hits
							}}</el-button>
						</template>
					</el-table-column>
					<el-table-column fixed="right" label="操作" width="160" align="center">
						<template #default="scope">
							<el-button
								link
								type="danger"
								plain
								@click="urlStatisticsDel(scope.row, scope.$index)"
								>删除</el-button
							>
						</template>
					</el-table-column>
				</el-table>
			</el-row>
			<el-row class="page">
				<el-text class="pgTxt">{{ pgTxt }}</el-text>
				<el-pagination
					class="pagination"
					background
					:currentPage="statisticsForm.currentPage"
					:page-size="statisticsForm.pageSize"
					layout="prev, pager, next, jumper"
					:total="statisticsForm.total"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				/>
			</el-row>
		</el-dialog>

		<el-dialog
			class="dialog"
			v-model="detailForm.visible"
			title="Url统计详情"
			width="1150"
			center
		>
			<el-row class="header">
				<el-button link type="success" :icon="Tickets" @click="exportHandle(2)"
					>导出Excel</el-button
				>
				<el-button link type="primary" :icon="Refresh" @click="detailRefresh"
					>刷新</el-button
				>
			</el-row>
			<el-row class="list">
				<el-table
					:data="detailForm.data"
					:row-style="{ height: '20px' }"
					style="width: 100%"
					height="520"
					row-key="ID"
					border
				>
					<el-table-column fixed prop="ForumName" label="论坛名" width="85">
						<template #default="scope">
							<el-tooltip :content="scope.row.ForumName" placement="top">
								<div class="ellipsis-tooltip">
									{{ scope.row.ForumName }}
								</div>
							</el-tooltip>
						</template>
					</el-table-column>
					<el-table-column prop="OriginalUrl" label="原地址">
						<template #default="scope">
							<el-tooltip :content="scope.row.OriginalUrl" placement="top">
								<div class="ellipsis-tooltip">
									{{ scope.row.OriginalUrl }}
								</div>
							</el-tooltip>
						</template>
					</el-table-column>
					<el-table-column label="目标地址" width="150">
						<template #default="scope">
							<el-tooltip :content="scope.row.TargetUrl" placement="top">
								<div class="ellipsis-tooltip" width="150">
									{{ scope.row.TargetUrl }}
								</div>
							</el-tooltip>
						</template>
					</el-table-column>
					<el-table-column label="Query" width="150">
						<template #default="scope">
							<el-tooltip :content="scope.row.Query" placement="top">
								<div class="ellipsis-tooltip" width="150">
									{{ scope.row.Query }}
								</div>
							</el-tooltip>
						</template>
					</el-table-column>
					<el-table-column label="Referrer" width="150">
						<template #default="scope">
							<el-tooltip :content="scope.row.Referrer" placement="top">
								<div class="ellipsis-tooltip" width="150">
									{{ scope.row.Referrer }}
								</div>
							</el-tooltip>
						</template>
					</el-table-column>
					<el-table-column prop="IPAddress" label="IPAddress" width="130">
						<template #default="scope">
							<el-tooltip :content="scope.row.IPAddress" placement="top">
								<div class="ellipsis-tooltip">
									{{ scope.row.IPAddress }}
								</div>
							</el-tooltip>
						</template>
					</el-table-column>
					<el-table-column label="浏览器" width="85">
						<template #default="scope">
							<el-tooltip :content="scope.row.BrowserType" placement="top">
								<div class="ellipsis-tooltip">
									{{ scope.row.BrowserType }}
								</div>
							</el-tooltip>
						</template>
					</el-table-column>
					<el-table-column prop="Platform" label="操作系统" width="85" />
					<el-table-column prop="Resolution" label="分辨率" width="90" />
					<el-table-column prop="VisitTime" label="访问时间" width="100">
						<template #default="scope">
							<el-tooltip :content="formatter2(scope.row)" placement="top">
								<div class="ellipsis-tooltip">
									{{ formatter2(scope.row) }}
								</div>
							</el-tooltip>
						</template>
					</el-table-column>
				</el-table>
			</el-row>
			<el-row class="page">
				<el-pagination
					class="pagination"
					background
					:currentPage="detailForm.currentPage"
					:page-size="detailForm.pageSize"
					layout="total, sizes, prev, pager, next, jumper"
					:total="detailForm.total"
					@size-change="handleSizeChange2"
					@current-change="handleCurrentChange2"
				/>
			</el-row>
		</el-dialog>

		<cl-upsert ref="Upsert"></cl-upsert>
	</cl-crud>
</template>

<script lang="ts" name="sys-menu" setup>
import { isArray } from "lodash-es";
import { ElMessage, ElMessageBox } from "element-plus";
import { useClipboard } from "@vueuse/core";
import { ref, reactive, onMounted } from "vue";
import { DocumentCopy, Search, Refresh, Tickets } from "@element-plus/icons-vue";
import { useCrud, useTable, useUpsert } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { utcToDate } from "/@/cool/utils";

const { service } = useCool();
const { copy } = useClipboard();

const shortcuts = [
	{
		text: "上一周",
		value: () => {
			const end = new Date();
			const start = new Date();
			start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
			return [start, end];
		}
	},
	{
		text: "上个月",
		value: () => {
			const end = new Date();
			const start = new Date();
			start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
			return [start, end];
		}
	},
	{
		text: "近三个月",
		value: () => {
			const end = new Date();
			const start = new Date();
			start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
			return [start, end];
		}
	}
];

const pgTxt = ref("");

const statisticsForm = reactive({
	data: [],
	visible: false,
	title: "",
	daterange: "",
	filter: 0,
	currentPage: 1,
	pageSize: 100,
	total: 0
});

const detailForm = reactive({
	data: [],
	visible: false,
	title: "",
	daterange: "",
	filter: 0,
	createTime: "",
	currentPage: 1,
	pageSize: 100,
	total: 0
});

const queryData = ref([]);

const Crud = useCrud(
	{
		service: service.base.common.go,
		dict: {
			api: {
				add: "urlAdd",
				update: "urlUpdate",
				delete: "urlDelete"
			},
			tips: {
				deleteConfirm: "是否继续删除此数据？"
			}
		},
		permission: {
			add: true,
			delete: true,
			update: true
		},
		onRefresh(_, { render }) {
			service.base.common.go.urlPage().then((list) => {
				render(list);
			});
		},
		onDelete(selection, { next }) {
			next({
				IDs: selection.map((e) => e.ID)
			});
		}
	},
	(app) => {
		app.refresh();
	}
);

const Table = useTable({
	columns: [
		{
			prop: "OriginalUrl",
			label: "原地址",
			align: "left",
			minWidth: 200
		},
		{
			prop: "TargetUrl",
			label: "目标地址",
			align: "left",
			minWidth: 160,
			showOverflowTooltip: true
		},
		{
			prop: "Describe",
			label: "描述",
			width: 200,
			showOverflowTooltip: true
		},
		{
			prop: "copy",
			label: "复制/统计",
			width: 90
		},
		{
			prop: "Enable",
			label: "状态",
			width: 80,
			component: {
				name: "cl-switch"
			}
		},
		{
			label: "操作",
			type: "op",
			width: 230,
			buttons: ["slot-add", "edit", "delete"]
		}
	]
});

const Upsert = useUpsert({
	dialog: {
		width: "600px"
	},
	onInfo(data, { done }) {
		done(data);
	},
	items: [
		{
			prop: "OriginalUrl",
			label: "原地址",
			component: {
				name: "el-input"
			},
			required: true
		},
		{
			prop: "TargetUrl",
			label: "目标地址",
			component: {
				name: "el-input"
			}
		},
		{
			prop: "Describe",
			label: "描述",
			component: {
				name: "el-input",
				props: {
					rows: "8",
					type: "textarea"
				}
			}
		}
	],
	onOpened(data) {
		if (!("FatherID" in data)) {
			Upsert.value?.setTitle("新增根节点");
		} else if ("FatherID" in data && !("ID" in data)) {
			service.base.common.go
				.urlByID({
					ID: data.FatherID
				})
				.then((res) => {
					Upsert.value?.setTitle(res.OriginalUrlPath);
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		} else if ("ID" in data) {
			Upsert.value?.setTitle(data.OriginalUrlPath);
		}
		console.log(data);
	}
});

const searchClick = (row: any) => {
	resetStatisticsForm();

	statisticsForm.visible = true;
	statisticsForm.title = row.OriginalUrlPath;

	searchRefresh();
};

const searchRefresh = () => {
	service.base.common.go
		.statisticByUrl({
			OriginalUrl: statisticsForm.title,
			filter: statisticsForm.filter,
			daterange: statisticsForm.daterange,
			page: statisticsForm.currentPage,
			size: statisticsForm.pageSize
		})
		.then((res) => {
			statisticsForm.data = [];
			statisticsForm.data = res?.list;
			statisticsForm.total = res?.total;

			pgTxt.value = `${res?.total}天 ${res?.sum}点击`;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});

	service.base.common.go
		.queryGroup({
			OriginalUrl: statisticsForm.title,
			filter: statisticsForm.filter,
			daterange: statisticsForm.daterange,
		})
		.then((res) => {
			queryData.value = res.sort((a, b) => b.count - a.count);
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const detailRefresh = () => {
	service.base.common.go
		.detailByUrl({
			OriginalUrl: statisticsForm.title,
			filter: statisticsForm.filter,
			daterange: statisticsForm.daterange,
			CreateTime: detailForm.createTime,
			page: detailForm.currentPage,
			size: detailForm.pageSize
		})
		.then((res) => {
			detailForm.data = [];
			detailForm.data = res?.list;
			detailForm.total = res?.total;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const exportHandle = (form: number) => {
	if (form === 1 || form === 3) {
		exportExcel(statisticsForm.title, form);
	} else if (form === 2) {
		exportExcel(detailForm.title, form);
	}
};

const exportExcel = (title: string, form: number) => {
	console.log(title, form);
	service.base.common.go
		.export({
			OriginalUrl: title,
			daterange: statisticsForm.daterange,
			form,
			detailFrom: detailForm.createTime,
			detailFilter: statisticsForm.filter
		})
		.then((res) => {
			let uint8Array = new Uint8Array(res.file.data);

			const blob = new Blob([uint8Array], {
				type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
			});

			const link = document.createElement("a");
			link.href = window.URL.createObjectURL(blob);
			link.download = res.fileName;
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const urlStatisticsDel = (row: any, index: any) => {
	ElMessageBox.confirm("确定删除这条记录吗?", "提示", {
		confirmButtonText: "确定",
		cancelButtonText: "取消",
		type: "warning"
	})
		.then(() => {
			service.base.common.go
				.urlStatisticsDel({
					ID: row.ID
				})
				.then(() => {
					statisticsForm.data.splice(index, 1);

					ElMessage({
						message: "删除成功",
						type: "success"
					});
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		})
		.catch(() => {});
};

const filter = (type: number) => {
	statisticsForm.filter = type;

	searchRefresh();
};

const hitsClick = (row: any) => {
	resetDetailForm();

	detailForm.visible = true;
	detailForm.title = statisticsForm.title;
	detailForm.createTime = row.CreateTime;

	detailRefresh();
};

const formatter = (row: any) => {
	return row.CreateTime.toString().endsWith("000000")
		? utcToDate(row.CreateTime, "YYYY-MM-DD")
		: row.CreateTime;
};

const formatter2 = (row: any) => {
	return utcToDate(row.Date, "YYYY-MM-DD");
};

const handleSizeChange = (val: number) => {
	statisticsForm.pageSize = val;
	searchRefresh();
};
const handleCurrentChange = (val: number) => {
	statisticsForm.currentPage = val;
	searchRefresh();
};

const handleSizeChange2 = (val: number) => {
	detailForm.pageSize = val;
	detailRefresh();
};
const handleCurrentChange2 = (val: number) => {
	detailForm.currentPage = val;
	detailRefresh();
};

function onRowClick(row: any, column: any) {
	if (column?.property && row.children) {
		Table.value?.toggleRowExpansion(row);
	}
}

function append({ ID }: any) {
	Crud.value?.rowAppend({
		FatherID: ID
	});
}

const copyClick = (row: any) => {
	const url = `https://g.chasedream.com/${row.OriginalUrlPath}`;
	copy(url);

	ElMessage({
		message: "已复制",
		type: "success"
	});
};

const deepTree = (list: any[]): any[] => {
	const newList: any[] = [];
	const map: any = {};

	const customSort = (a: any, b: any): number => {
		if (a.leaf !== b.leaf) {
			return a.leaf ? 1 : -1;
		}
		return a.OriginalUrl.localeCompare(b.OriginalUrl);
	};

	list.forEach((e) => (map[e.ID] = e));

	list.forEach((e) => {
		e.leaf = !isArray(e.children);
		const parent = map[e.FatherID];
		if (parent) {
			parent.leaf = false;
			(parent.children || (parent.children = [])).push(e);
			parent.children.sort(customSort);
		} else {
			newList.push(e);
		}
	});

	return newList;
};

const resetForms = () => {
	resetStatisticsForm();
	resetDetailForm();
};

const resetStatisticsForm = () => {
	statisticsForm.data = [];
	statisticsForm.visible = false;
	statisticsForm.title = "";
	statisticsForm.daterange = "";
	statisticsForm.filter = 0;
	statisticsForm.currentPage = 1;
	statisticsForm.pageSize = 100;
	statisticsForm.total = 0;
};

const resetDetailForm = () => {
	detailForm.data = [];
	detailForm.visible = false;
	detailForm.title = "";
	detailForm.daterange = "";
	detailForm.filter = 0;
	detailForm.createTime = "";
	detailForm.currentPage = 1;
	detailForm.pageSize = 100;
	detailForm.total = 0;
};

const getTagType = (count: number) => {
    if (count >= 7) return '';  // primary color
    if (count >= 4) return 'success';
    if (count >= 2) return 'warning';
    return 'info';
};

onMounted(() => {
	resetForms();
});
</script>

<style lang="scss" scoped>
@import "../css/index.scss";
</style>
