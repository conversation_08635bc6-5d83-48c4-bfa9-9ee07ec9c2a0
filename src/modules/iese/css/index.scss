.wrapper {
	margin: 10px 5px 10px 5px;
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	overflow-y: auto;

	.opt {
		margin-bottom: 10px;
	}

	.list {
		display: flex;
		height: calc(100% - 70px);
		overflow: hidden;
		flex: 1;
	}

	.page {
		display: flex;
		height: 70px;
		padding-bottom: 10px;
		margin-right: 10px;
		justify-content: right;
	}
}

.statistics {
	text-align: center;
	margin: 0 auto;
	margin-bottom: 8px;

	:deep(.num) {
		color: chocolate;
		font-size: 18px;
		margin-right: 8px;
	}

	:deep(.text) {
		width: 80px;
	}
}

.subject {
	font-size: 12px;
}

.pushBtn {
	height: 30px;
	width: 28px;
	margin: 2px 0;
}

.colorWrap {
	display: flex;
	flex-wrap: wrap;
	width: 90px;
}

.cursor-pointer {
	cursor: pointer;
}

.ellipsis-tooltip {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.el-row {
	margin-bottom: 5px;
}

.el-radio {
	margin-right: 10px;
}

.el-dropdown-link {
	cursor: pointer;
	color: var(--el-color-primary);
	display: flex;
	align-items: center;
}

.form-submit {
	display: flex;
	justify-content: flex-end;
	width: 100%;
}

.locationWrap {
	padding: 20px 0 10px 0;
	margin-bottom: 20px;
	border: 1px solid #efefef;

	.location {
		.el-select {
			width: 200px;
		}
	}

	.el-checkbox {
		margin-right: 5px;
	}

	.location-header {
		position: relative;
	}

	.delete-icon {
		position: absolute;
		right: 10px;
		top: 0;
		z-index: 999;
	}
}

.material {
	position: relative;
	width: 100px;
	height: 100px;

	&:hover {
		&:after {
			content: "";
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(255, 255, 255, 0.8);
		}

		.del {
			display: block;
		}
	}

	.del {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 20px;
		height: 20px;
		line-height: 20px;
		text-align: center;
		font-size: 12px;
		color: crimson;
		display: none;
		cursor: pointer;
		z-index: 99;
	}
}

.color {
	background-image: url(/right.png);
	background-repeat: no-repeat;
	background-position: bottom right;
	background-size: 140%;
}

.split {
	margin: 0 5px;
}

:deep(.el-form-item__label) {
	font-size: 12px;
}

:deep(.el-tabs__item) {
	font-size: 17px;
}

:deep(.el-radio__label) {
	font-weight: normal;
}