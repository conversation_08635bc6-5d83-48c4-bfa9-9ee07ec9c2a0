<template>
	<div class="wrapper">
		<el-row class="opt">
			<el-col :span="12">
				<el-button @click="router.push('/console/iese/blog/add')" type="primary"
					>添加</el-button
				>
			</el-col>
		</el-row>
		<el-row class="list">
			<el-col :span="24">
				<el-table
					border
					style="width: 100%"
					:data="tableData"
					:max-height="tableHeight"
					:row-style="{ height: '40px' }"
					:header-cell-style="{ background: '#ebeef5', color: '#333' }"
				>
					<el-table-column fixed prop="id" label="ID" width="80" align="center">
					</el-table-column>
                    <el-table-column width="110" label="发布时间" align="center">
						<template #default="scope">
							{{ formatter(scope.row.datetime) }} 
						</template>
					</el-table-column>
                    <el-table-column width="110" label="展示图片" align="center">
						<template #default="scope">
							<img :src="scope.row.pic"                                
                                style="width: 80px; height: 50px; object-fit: cover" />
						</template>
					</el-table-column>
					<el-table-column label="标题" align="left">
                        <template #header>
							<el-input
								v-model="search.title"
								size="small"
								placeholder="标题"
								clearable
								@change="refresh"
							/>
						</template>
						<template #default="scope">
                            <el-link :href="'https://iesebs.cn/blog/' + scope.row.uuid" target="_blank" type="primary">{{ scope.row.title }}</el-link>							
						</template>
					</el-table-column>
					<el-table-column width="110" label="作者" align="center">
						<template #default="scope">
							{{ scope.row.author }} 
						</template>
					</el-table-column>
                    <el-table-column width="110" label="创建时间" align="center">
						<template #default="scope">
							{{ scope.row.createTime }} 
						</template>
					</el-table-column>
					<el-table-column fixed="right" label="操作" width="100" align="center">
						<template #default="scope">
							<el-icon
								@click="editClick(scope.row)"
								class="cursor-pointer"
								style="color: #464bd7; margin-right: 8px"
								:size="20"
								><edit
							/></el-icon>

							<el-popconfirm
										title="确定要删除吗?"
										@confirm="deleteHandler(scope.row)"
									>
										<template #reference>
											<el-icon
												class="cursor-pointer"
												style="color: crimson; margin-right: 8px"
												:size="20"
												><delete
											/></el-icon>
										</template>
									</el-popconfirm>
						</template>
					</el-table-column>
				</el-table>
			</el-col>
		</el-row>
		<el-row class="page">
			<el-pagination
				class="pagination"
				background
				:currentPage="currentPage"
				:page-size="pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</el-row>
	</div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import { Edit, Delete, Search } from "@element-plus/icons-vue";
import { ref, reactive, onMounted, nextTick } from "vue";
import { useCool } from "/@/cool";
import { removeEmptyFromObject } from "/@/cool/utils";
import { datelineToDate } from "/@/cool/utils";

const { service, router } = useCool();

const search = reactive({
	id: "",
	title: ""
});

const tableHeight = ref(0);
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(50);
const total = ref(0);

const formatter = (datetime: any) => {
	return datelineToDate(datetime, "YYYY-MM-DD HH:mm:ss");
};

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};

const editClick = (row: any) => {
	router.push(`/console/iese/blog/edit?id=${row.id}`);
};

const deleteHandler = (row: any) => {
	service.base.common.iese
		.del({
			id: row.id
		})
		.then((res) => {
			ElMessage({
				message: "已删除!",
				type: "success"
			});
			refresh();
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.iese
		.pages({
			...s,
			page: currentPage.value,
			pageSize: pageSize.value
		})
		.then((res) => {
			tableData.value = res[0];
			total.value = res[1];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};


refresh();

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 250;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 250;
		};
	});
});
</script>

<style lang="scss" scoped>
@import "../css/index.scss";
</style>
