<template>
	<div class="wrapper">
		<div class="form-container">
			<el-form :model="form" label-width="100px" :rules="rules" ref="formRef">
				<el-form-item label="展示图" :required="true" prop="pic">
					<div v-if="form.pic?.length > 0" class="material">
							<el-image
								style="width: 120px; height: 80px"
								:src="form.pic"
								fit="cover"
							/>
							<el-icon class="del" size="20" @click="delPic()"
								><Delete
							/></el-icon>
						</div>
						<el-icon
							v-else
							class="cursor-pointer"
							size="30"
							color="#CFD3DC"
							@click="addPic()"
							><CirclePlus
						/></el-icon>
						<input
							type="file"
							multiple
							accept="image/*"
							:ref="setRefs('fileInput')"
							style="display: none"
							@change="handleFileChange($event, 1)"
						/>
				</el-form-item>

				<el-form-item label="标题" :required="true" prop="title">
					<el-input v-model="form.title" placeholder="请输入标题" />
				</el-form-item>

				<el-form-item label="内容" :required="true" prop="content">
					<div id="vditor" class="vditor-container"></div>
				</el-form-item>

				<el-form-item label="作者" :required="true" prop="author">
					<el-input v-model="form.author" placeholder="请输入作者" />
				</el-form-item>

				<el-form-item label="发布日期" :required="true" prop="datetime">
					<el-date-picker
						v-model="form.datetime"
						type="datetime"
						placeholder="请选择发布日期"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
					/>
				</el-form-item>

				<el-form-item label="标签">
					<el-checkbox-group v-model="form.tags">
						<el-checkbox
							v-for="obj in tags"
							:key="obj.id"
							:label="obj.name"
							:value="obj.id"
						/>
					</el-checkbox-group>
				</el-form-item>

				<el-form-item style="margin-top: 40px">
					<el-button type="primary" @click="handleSubmit(formRef)">提交</el-button>
					<el-button @click="router.back()">取消</el-button>
				</el-form-item>
			</el-form>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from "vue";
import { CirclePlus, Delete, Plus, CloseBold } from "@element-plus/icons-vue";
import type { FormInstance } from "element-plus";
import { ElMessage } from "element-plus";
import { useCool } from "/@/cool";
import { isDev } from "/@/config";
import _ from "lodash";
import Vditor from "vditor";
import "vditor/dist/index.css";

const { service, router, refs, setRefs } = useCool();

let vd = ref();

const cUrl = isDev
				? "http://localhost:9000/dev/admin/base/open/upload4IESEPic"
				: "https://connect.chasedream.com/api/v2/admin/base/open/upload4IESEPic";

const form = reactive({	
	id: 0,	
	pic: "",
	title: "",
	summary: "",
	content: "",
	author: "",
	datetime: "",
	tags: []
});

const rules = reactive({	
	pic: [{ required: true, message: "请上传展示图", trigger: "blur" }],
	title: [{ required: true, message: "请输入标题", trigger: "blur" }],	
	content: [{ required: true, message: "请输入内容", trigger: "blur" }],
	author: [{ required: true, message: "请输入作者", trigger: "blur" }],
	datetime: [{ required: true, message: "请选择发布日期", trigger: "blur" }]
});

const formRef = ref<FormInstance>();

const tags = ref([]);

const initVditor = async () => {
  await nextTick();
  vd.value = new Vditor("vditor", {
    height: 400,
    width: '100%',
    mode: 'sv',
    preview: {
      mode: 'both',
      hljs: {
        style: 'github'
      }
    },
    cache: {
      enable: false
    },
    after: () => {
	  loadData();
    },
    input: (value) => {
      form.content = value;
    },
    upload: {
      url: cUrl,
      accept: 'image/*',
      success: (editor: HTMLPreElement, msg: string) => {
        const response = JSON.parse(msg);
        if (response.code === 1000 && response.data.success) {
          const imageUrl = response.data.image;
          // Insert image URL into editor
          vd.value.insertValue(`![image](${imageUrl})`);
        }
      }
    }
  });
};

const handleSubmit = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	
	form.content = vd.value.getValue();

	await formEl.validate((isValid, fields) => {
		if (!isValid) return;

		service.base.common.iese
			.edit({
				...form
			})
			.then((res) => {
				ElMessage({
					message: "已修改!",
					type: "success"
				});
				router.back();
			})
			.catch((err) => {
				ElMessage.error(err.message);
			});
	});
};

const handleFileChange = async (event: any, type) => {
	const files = event.target.files;
	if (files) {
		await upload(files, type);
	}

	event.target.value = "";
};

const upload = async (files: any, type) => {
	for (let i = 0; i < files.length; i++) {
		const formData = new FormData();
		formData.append("files", files[i]);

		try {			
			const response = await fetch(cUrl, {
				method: "POST",
				body: formData
			});

			if (response.ok) {
				const res = await response.json();
				if (type === 1) {
					form.pic = res.data.image;
				} else if (type === 2) {
					form.pic = res.data.image;
				}
			} else {
				ElMessage.error("Upload failed");
			}
		} catch (err: any) {
			ElMessage.error(err.message);
		}
	}
};

const delPic = () => {
	form.pic = "";
};

const addPic = () => {
	const el = refs[`fileInput`];
	el?.click();
};

const getQueryParam = (param) => {
	const urlParams = new URLSearchParams(window.location.search);
	return urlParams.get(param);
};

const loadTags = () => {
	service.base.common.iese
		.tags()
		.then((res) => {
			tags.value = res;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const loadData = async () => {
    service.base.common.iese
        .findOne({
            id: getQueryParam("id")
        })
        .then((res) => {
			form.id = res.id;
            form.pic = res.pic;
            form.title = res.title;
            form.summary = res.summary;
            form.content = res.content;
            form.author = res.author;
			form.tags = res.tags.map((item) => item.tagId);
			form.datetime = res.datetime;

			vd.value.setValue(form.content);
        })
        .catch((err) => {
            ElMessage.error(err.message);
        });
};

onMounted(() => {
	loadTags();
    initVditor();
});

</script>

<style lang="scss" scoped>
@import "../css/index.scss";
</style>
