.chatgpt {
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;
}

.chatgpt-header {
	display: flex;
	justify-content: left;
	align-items: center;
	height: 50px;
	background-color: #eff0f3;
	font-size: 20px;
	h2 {
		margin-left: 15px;
	}
	.sub {
		font-size: 14px;
		margin-left: 8px;
	}
}

.chatgpt-messages {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow-y: auto;
	padding: 10px;
	.preview {
		margin-top: 2px;
	}
	.date{
		font-size: 12px;
		color: #909399;
		margin-bottom: 8px;
	}
}

.chatgpt-message {
	margin-bottom: 10px;
	padding: 10px;
	border-radius: 10px;
}

.chatgpt-message-user {
	background-color: #fff;
}

.chatgpt-message-sys {
	background-color: #F7F7F8;
}

.chatgpt-input-container {
	display: flex;
	justify-content: center;
	align-items: center;
	position: relative;
	padding: 10px;
}

:deep(.github-markdown-body) {
	padding: 0;
}

.refresh{
	border: none;
	:deep(.el-icon) {
		font-size: 20px;
	}
}

.btnExpand{
	position: absolute;
	top: 15px;
	right: 12px;
	width: 40px;
	height: 40px;
	border: none;
	:deep(.el-icon) {
		font-size: 25px;
	}
}

.btnSend {
	position: absolute;
	bottom: 13px;
	right: 12px;
	width: 40px;
	height: 40px;
	border: none;
	:deep(.el-icon) {
		font-size: 25px;
	}
}

.container {
	display: flex;
	flex-direction: column;
	height: 100%;
}

.scroll {
	flex: 1;
}

.pagination {
	height: 40px;
	margin: 0 10px;
}

.left {
	cursor: pointer;
	.new {
		display: flex;
		font-size: 15px;
		align-items: center;
		height: 50px;
		padding-right: 10px;
		
		.users {
			width: 100%;
		}
	}
	.search{
		margin-top: 5px;
		margin-right: 10px;
	}
	.list {
		position: relative;
		font-size: 14px;
		height: 50px;
		padding: 5px 15px 5px 15px;
		&.is-active {
			background-color: var(--color-primary);
			color: #fff;
		}
		&:not(.is-active):hover {
			background-color: var(--el-fill-color-light);
		}
		&.is-on {
			background-color: var(--el-fill-color-light);
		}
		&.is-del {
			background-color: #E6E8EB;
		}
		.subject{
			padding-bottom: 5px;
			white-space: nowrap;
  			overflow: hidden;
  			text-overflow: ellipsis;
		}
		.sub{
			font-size: 12px;
			white-space: nowrap;
  			overflow: hidden;
  			text-overflow: ellipsis;
			color:#909399;
		}
		.digest{
			position: absolute;
			left: 0px;
			top: -3px;
			width: 10px;
			height: 10px;
		}
		.dateline{
			position: absolute;
			color:#909399;
			font-size: 10px;
			right: 10px;
			top: 5px;
		}
	}
}

.tag{
	cursor: pointer;
	margin-bottom: 10px;
	margin-right: 8px;
}

.modelSel{
	width: 70px;
}