<template>
	<div class="view-search">
		<div class="wrapper">
			<el-row class="opt">
				<el-button type="primary" @click="openDialog(1)">新增</el-button>
			</el-row>
			<el-row class="list">
				<el-table
					border
					:data="tableData"
					:max-height="tableHeight"
					:row-style="{ height: '40px' }"
					:header-cell-style="{ background: '#ebeef5', color: '#333' }"
				>
					<el-table-column fixed prop="id" label="ID" width="70" align="center">
						<template #header>
							<el-input
								v-model="search.id"
								size="small"
								placeholder="ID"
								clearable
								@change="refresh"
							/>
						</template>
					</el-table-column>
					<el-table-column prop="username" label="用户名" align="left">
						<template #header>
							<el-input
								v-model="search.username"
								size="small"
								placeholder="用户名"
								clearable
								@change="refresh"
							/>
						</template>
					</el-table-column>

					<el-table-column fixed="right" label="操作" width="140" align="center">
						<template #default="scope">
							<el-button link type="primary" plain @click="editHandler(scope.row)"
								>编辑</el-button
							>
							<el-button link type="danger" plain @click="deleteHandler(scope.row.id)"
								>删除</el-button
							>
						</template>
					</el-table-column>
				</el-table>
			</el-row>
			<el-row class="page">
				<el-pagination
					class="pagination"
					:hide-on-single-page="true"
					background
					:currentPage="currentPage"
					:page-size="pageSize"
					layout="total, sizes, prev, pager, next, jumper"
					:total="total"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				/>
			</el-row>
			<cl-dialog :title="title" v-model="visible">
				<el-form :model="form" :rules="rules" ref="formRef" label-width="80px" status-icon>
					<el-form-item label="用户名" prop="username">
						<el-input v-model="form.username" />
					</el-form-item>

					<el-form-item label="密码" prop="password">
						<el-input v-model="form.password" type="password" />
					</el-form-item>

					<el-form-item>
						<cl-flex1 />
						<el-button @click="closeDialog">取消</el-button>
						<el-button type="success" @click="submitForm(formRef)">保存</el-button>
					</el-form-item>
				</el-form>
			</cl-dialog>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { ref, reactive, onMounted, nextTick, computed } from "vue";
import { useCool } from "/@/cool";
import { removeEmptyFromObject } from "/@/cool/utils";

const { service } = useCool();

const search = reactive({
	id: "",
	username: ""
});

const formRef = ref<FormInstance>();
const form = reactive({
	id: 0,
	username: "",
	password: ""
});

const tableHeight = ref(0);
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);
const visible = ref(false);
const status = ref(0);

const title = computed(() => {
	return status.value === 1 ? "新增" : "编辑";
});

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.chatgpt
		.gptsAuthPage({
			...s,
			page: currentPage.value,
			size: pageSize.value
		})
		.then((res) => {
			tableData.value = res[0];
			total.value = res[1];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

refresh();

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};

const openDialog = (val: any) => {
	status.value = val;
	if (val === 1) {
		rules.password = [{ required: true, message: "请输入密码", trigger: "blur" }];
	}
	visible.value = true;
};

const closeDialog = () => {
	status.value = 0;
	visible.value = false;

	resetForm();
};

const resetForm = () => {
	form.id = 0;
	form.username = "";
	form.password = "";
};

const editHandler = (row: any) => {
	form.id = row.id;
	form.username = row.username;
	rules.password = [{ required: false, message: "请输入密码", trigger: "blur" }];

	openDialog(2);
};

const deleteHandler = (id: number) => {
	ElMessageBox.confirm("此操作将永久删除选中数据，是否继续？", {
		title: "提示",
		confirmButtonText: "确定",
		cancelButtonText: "取消",
		type: "warning"
	})
		.then(() => {
			service.base.common.chatgpt
				.gptsAuthDelete({
					id
				})
				.then((res) => {
					ElMessage({
						message: "删除成功",
						type: "success"
					});
					refresh();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		})
		.catch(() => {});
};

const rules = reactive<FormRules>({
	username: [{ required: true, message: "请输入用户名", trigger: "blur" }]
});

const submitForm = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate((valid, fields) => {
		if (valid) {
			if (status.value === 1) {
				service.base.common.chatgpt
					.gptsAuthAdd({
						...form
					})
					.then((res) => {
						ElMessage({
							message: "新增成功",
							type: "success"
						});
						closeDialog();
						resetForm();
						refresh();
					})
					.catch((err) => {
						ElMessage.error(err.message);
					});
			} else {
				service.base.common.chatgpt
					.gptsAuthUpdate({
						...form
					})
					.then((res) => {
						ElMessage({
							message: "修改成功",
							type: "success"
						});
						closeDialog();
						resetForm();
						refresh();
					})
					.catch((err) => {
						ElMessage.error(err.message);
					});
			}
		} else {
			console.log("error submit!", fields);
		}
	});
};

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 230;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 230;
		};
	});
});
</script>

<style lang="scss" scoped>
.view-search {
	display: flex;
	background-color: #fff;
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	.wrapper {
		margin: 10px 5px 10px 5px;
		display: flex;
		flex-direction: column;
		width: 100%;
		height: 100%;
		.opt {
			margin-bottom: 10px;
		}
		.list {
			display: flex;
			height: calc(100% - 70px);
			overflow: hidden;
			flex: 1;
		}
		.page {
			display: flex;
			height: 70px;
			padding-bottom: 10px;
			justify-content: right;
		}
	}
}

.cursor-pointer {
	cursor: pointer;
	color: var(--color-primary);
}
</style>
