<template>
	<cl-view-group ref="ViewGroup">
		<template #left>
			<div class="container">
				<el-scrollbar class="scroll">
					<div class="left">
						<div class="new" @click="newConversation">
							<el-icon><Plus /></el-icon>New chat
						</div>
						<div class="search">
							<el-input
								clearable
								v-model="search"
								placeholder="Search"
								@keyup.enter.native="loadSessions"
								@clear="loadSessions"
							>
								<template #prepend>
									<el-button :icon="Search" />
								</template>
							</el-input>
						</div>
						<div
							v-for="(session, index) in sessions"
							:key="index"
							class="list"
							:class="{
								'is-on': session.isactive
							}"
							@contextmenu.stop.prevent="onContextMenu($event, session)"
							@click="loadDetails(session)"
						>
							<div class="subject">{{ session.subject }}</div>
							<div class="sub">{{ session.lasMsgText }}</div>
							<div class="digest" v-if="session.digest">
								<img src="/@/assets/corner-left-top.svg" />
							</div>
							<div class="dateline">
								{{ datelineFormat(session.lastMsgTime) }}
							</div>
						</div>
					</div>
				</el-scrollbar>
				<el-pagination
					class="pagination"
					:hide-on-single-page="true"
					background
					:currentPage="currentPage"
					:page-size="pageSize"
					layout="prev, pager, next"
					:total="total"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				/>
			</div>
		</template>
		<template #title>
			<span class="title">{{ subject }}</span>
		</template>
		<template #right-head-right>
			<el-select
				v-model="aiVenderVal"
				:disabled="modelS"
				class="modelSel"
				placeholder="供应商"
				size="small"
				style="width: 95px; margin-right: 8px"
				v-permission="service.base.common.chatgpt.permission.chatgpt4"
				@change="loadAIVenderModel"
			>
				<el-option
					v-for="item in aiVender"
					:key="item.value"
					:label="item.label"
					:value="item.value"
				/>
			</el-select>

			<el-select
				v-model="aiVenderModelVal"
				:disabled="modelS"
				class="modelSel"
				placeholder="模型"
				size="small"
				style="width: 120px; margin-right: 8px"
				v-permission="service.base.common.chatgpt.permission.chatgpt4"
			>
				<el-option
					v-for="item in aiVenderModel"
					:key="item.value"
					:label="item.label"
					:value="item.value"
				/>
			</el-select>

			<el-select
				v-if="isTTS"
				v-model="voiceVal"
				class="voiceSel"
				size="small"
				style="width: 80px"
			>
				<el-option
					v-for="item in voiceOptions"
					:key="item.value"
					:label="item.label"
					:value="item.value"
				/>
			</el-select>

			<el-button class="refresh" :icon="Refresh" @click="refreshDetails"
		/></template>
		<template #right>
			<div class="chatgpt">
				<div class="chatgpt-messages" v-if="!expandS.open">
					<el-scrollbar ref="scrollbarRef">
						<div
							class="chatgpt-message"
							v-for="(message, index) in messages"
							:key="index"
							:class="
								message.fromUser ? 'chatgpt-message-user' : 'chatgpt-message-sys'
							"
						>
							<div class="date" v-show="message.fromUser">
								{{ message.createTime }}
							</div>
							<el-row class="demo-avatar demo-basic">
								<el-col :span="2"
									><el-avatar
										v-if="message.fromUser"
										shape="square"
										:src="avatar" />
									<el-avatar
										v-else
										shape="square"
										src="https://forum.chasedream.com/static/chasedream/image/common/chatgpt.png?v=1"
								/></el-col>
								<el-col v-if="message.rawHtml" :span="22" class="preview 11">
									<div v-html="message.text"></div>
								</el-col>
								<el-col v-else :span="22" class="preview 22">
									<v-md-preview :text="message.text"></v-md-preview>
								</el-col>
							</el-row>
						</div>
					</el-scrollbar>
				</div>
				<div class="chatgpt-input-container">
					<el-input
						v-if="!expandS.open"
						ref="send"
						v-model="input"
						@keyup.enter.native="keyDown"
						type="textarea"
						placeholder="Type your message here...（Ctrl+Enter）"
						resize="none"
						:autosize="{ minRows: 4, maxRows: 10 }"
					>
					</el-input>
					<el-input
						v-if="expandS.open"
						ref="sendFull"
						v-model="input"
						@keyup.enter.native="keyDown"
						type="textarea"
						placeholder="Type your message here...（Ctrl+Enter）"
						resize="none"
						:rows="rows"
						:style="{ height: inputHeight }"
					>
					</el-input>
					<el-button
						v-if="isMultimodal"
						class="btnAttach"
						:icon="Paperclip"
						@click="attachClick(1)"
						v-permission="service.base.common.chatgpt.permission.chatgpt4"
					/>
					<input
						type="file"
						multiple
						accept="image/*"
						ref="fileInput"
						style="display: none"
						@change="handleFileChange"
					/>

					<el-button
						v-if="isWhisper"
						class="btnAttach"
						:icon="Paperclip"
						@click="attachClick(5)"
						v-permission="service.base.common.chatgpt.permission.voiceToText"
					/>
					<input
						type="file"
						accept=".mp3, .mp4, .mpeg, .mpga, .m4a, .wav, .webm"
						ref="fileInput2"
						style="display: none"
						@change="handleFileChange2"
					/>
					<el-button class="btnExpand" :icon="expandIcon" @click="expandToggle" />
					<el-button
						class="btnSend"
						:class="{
							btnSendEnable: !disabled
						}"
						:disabled="disabled"
						:icon="Promotion"
						@click="sendMessage"
					/>
					<div class="uploadFiles">
						<div
							v-for="(url, index) in uploadFiles"
							:key="index"
							class="wrap"
							@mouseenter="() => (showIcon[index] = true)"
							@mouseleave="() => (showIcon[index] = false)"
						>
							<el-image
								style="width: 40px; height: 40px"
								:src="url.url"
								fit="cover"
							/>
							<el-icon
								class="close"
								v-show="showIcon[index]"
								@click="closeHandler(index)"
								><Close
							/></el-icon>
						</div>
					</div>
				</div>
			</div>
		</template>
	</cl-view-group>
	<el-dialog v-model="dialogFormVisible" title="编辑会话" center>
		<el-form ref="ruleFormRef" :model="form">
			<el-form-item
				label="标题："
				label-width="140px"
				prop="subject"
				:rules="[
					{
						required: true,
						message: '请输入标题',
						trigger: 'blur'
					}
				]"
			>
				<el-input v-model="form.subject" autocomplete="off" />
			</el-form-item>
			<el-form-item
				label="置顶："
				label-width="140px"
				prop="digest"
				:rules="[
					{
						required: true,
						message: '请输入标题',
						trigger: 'blur'
					}
				]"
			>
				<el-checkbox v-model="form.digest" label="" size="large" />
			</el-form-item>
			<el-form-item label="标签：" label-width="140px">
				<el-tag
					v-for="(tag, index) in tags"
					:key="tag.id"
					class="tag"
					closable
					size="large"
					:type="tag.type"
					:effect="tag.effect"
					@click="toggleTag(tag)"
					@close="delTag(tag, index)"
				>
					{{ tag.tag }}
				</el-tag>
				<el-input
					v-if="newTagVisible"
					ref="newTagRef"
					v-model="newTagValue"
					size="small"
					@keyup.enter="newTag"
					@blur="newTag"
				/>
				<el-button v-else size="small" @click="showNewTag"> + New Tag </el-button>
			</el-form-item>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="dialogFormVisible = false">取消</el-button>
				<el-button type="primary" @click="save(ruleFormRef)"> 保存 </el-button>
			</span>
		</template>
	</el-dialog>
	<cl-form ref="Form" />
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox, ElScrollbar, ElInput } from "element-plus";
import type { FormInstance } from "element-plus";
import {
	Promotion,
	ArrowUp,
	ArrowDown,
	Plus,
	Refresh,
	Search,
	Paperclip,
	Close
} from "@element-plus/icons-vue";
import { ref, reactive, onMounted, computed, nextTick } from "vue";
import { ContextMenu, useForm } from "@cool-vue/crud";
import { useBase, checkPerm } from "/$/base";
import { useCool } from "/@/cool";
import { isDev } from "/@/config";
import { io } from "socket.io-client";
import { last } from "lodash-es";
import { today, datelineFormat } from "/@/cool/utils";
import { useViewGroup } from "/@/plugins/view";

const { user } = useBase();
const { service } = useCool();

const subject = ref<string>("聊效");

const { ViewGroup } = useViewGroup({
	custom: true
});

const scrollbarRef = ref<InstanceType<typeof ElScrollbar>>();
const search = ref("");
const send = ref<any>(null);
const sendFull = ref<any>(null);
const input = ref<string>("");
const parentMessageId = ref<string>("");
const disabled = ref<boolean>(false);
const messages = reactive<any[]>([]);
const sessions = reactive<any[]>([]);
const uploadFiles = reactive<any[]>([]);
const currentPage = ref(1);
const pageSize = ref(50);
const total = ref(0);

const fileInput = ref(null);
const fileInput2 = ref(null);
const showIcon = reactive({});

const voiceVal = ref("alloy");
const voiceOptions = [
	{ value: "alloy", label: "alloy" },
	{ value: "echo", label: "echo" },
	{ value: "fable", label: "fable" },
	{ value: "onyx", label: "onyx" },
	{ value: "nova", label: "nova" },
	{ value: "shimmer", label: "shimmer" }
];

const aiVenderVal = ref(1);
const aiVender = reactive<any[]>([]);

const aiVenderModelVal = ref(1);
const aiVenderModel = reactive<any[]>([]);

const sessionModelType = ref("");
const modelS = computed(() => messages.length > 0);

const dialogFormVisible = ref(false);
const ruleFormRef = ref<FormInstance>();
const form = reactive({
	id: 0,
	subject: "",
	digest: false
});

const tags = reactive<any[]>([]);
const tagType = ["", "success", "info", "warning", "danger"];

const newTagValue = ref("");
const newTagVisible = ref(false);
const newTagRef = ref<InstanceType<typeof ElInput>>();

const isMultimodal = computed(() => {
	const aivm = aiVenderModel.find(
		(el) => el.value == aiVenderModelVal.value && el.type === "multimodal"
	);

	return aivm?.value > 0;
});

const isWhisper = computed(() => {
	const aivm = aiVenderModel.find(
		(el) => el.value == aiVenderModelVal.value && el.type === "whisper"
	);

	return aivm?.value > 0;
});

const isTTS = computed(() => {
	const aivm = aiVenderModel.find(
		(el) => el.value == aiVenderModelVal.value && el.type === "tts"
	);

	return aivm?.value > 0;
});

const closeHandler = (index: number) => {
	uploadFiles.splice(index, 1);
};

const toggleTag = (tag: any) => {
	console.log(tag);

	if (tag.effect === "dark") {
		service.base.common.chatgpt
			.unbindTag({
				sessionid: form.id,
				tagid: tag.id
			})
			.then(() => {
				tag.effect = "plain";
			})
			.catch((err: any) => {
				ElMessage.error(err.message);
			});
	} else {
		service.base.common.chatgpt
			.bindTag({
				sessionid: form.id,
				tagid: tag.id
			})
			.then(() => {
				tag.effect = "dark";
			})
			.catch((err: any) => {
				ElMessage.error(err.message);
			});
	}
};

const delTag = (tag: any, idx: any) => {
	service.base.common.chatgpt
		.delTag({
			id: tag.id
		})
		.then(() => {
			tags.splice(idx, 1);
		})
		.catch((err: any) => {
			ElMessage.error(err.message);
		});
};

const showNewTag = () => {
	newTagVisible.value = true;
	nextTick(() => {
		newTagRef.value!.input!.focus();
	});
};

const avatar = user?.info?.avatarstatus
	? `https://forum.chasedream.com/uc_server/avatar.php?uid=${user.info.uid}&size=middle`
	: "https://forum.chasedream.com/static/chasedream/image/common/chatuser.png";

const expandS = reactive({
	open: false
});

const expandIcon = computed(() => (expandS.open ? ArrowDown : ArrowUp));

let socket: any = undefined;
let sessionId = 0;

const newTag = () => {
	if (newTagValue.value) {
		service.base.common.chatgpt
			.tag({
				tag: newTagValue.value,
				sessionid: form.id
			})
			.then((res: any) => {
				res.effect = "dark";
				res.type = tagType[Math.floor(Math.random() * tagType.length)];
				tags.push(res);
			})
			.catch((err: any) => {
				ElMessage.error(err.message);
			});
	}
	newTagVisible.value = false;
	newTagValue.value = "";
};

const keyDown = (e: any) => {
	if (e.ctrlKey && e.keyCode == 13) {
		sendMessage();
	}
};

const rows = ref(4);
const inputHeight = ref<any>(null);

const attachClick = (type: number) => {
	if (type === 1) {
		fileInput.value?.click();
	} else if (type === 5) {
		fileInput2.value?.click();
	}
};

const handleFileChange = async (event: any) => {
	const files = event.target.files;
	if (files) {
		await upload(files);
	}

	event.target.value = "";
};

const handleFileChange2 = async (event: any) => {
	const files = event.target.files;
	const isFileTooLarge = Array.from(files).some((file) => file.size > 25000000);

	if (isFileTooLarge) {
		ElMessage.error("文件不能大于25MB");
	} else {
		await upload2(files);
	}

	event.target.value = "";
};

const upload = async (files: any) => {
	const formData = new FormData();

	for (let i = 0; i < files.length; i++) {
		formData.append("files", files[i]);
	}

	try {
		const url = isDev
			? "http://localhost:9000/dev/admin/base/open/upload4GPT"
			: "https://connect.chasedream.com/api/v2/admin/base/open/upload4GPT";
		const response = await fetch(url, {
			method: "POST",
			body: formData
		});
		if (response.ok) {
			const res = await response.json();

			res.data.urls?.forEach((el: any) => {
				uploadFiles.push(el);
			});
		} else {
			ElMessage.error("Upload failed");
		}
	} catch (err: any) {
		ElMessage.error(err.message);
	}
};

const upload2 = async (files: any) => {
	const formData = new FormData();

	for (let i = 0; i < files.length; i++) {
		formData.append("files", files[i]);
	}

	try {
		const url = isDev
			? "http://localhost:9000/dev/admin/base/open/upload4GPT"
			: "https://connect.chasedream.com/api/v2/admin/base/open/upload4GPT";
		const response = await fetch(url, {
			method: "POST",
			body: formData
		});
		if (response.ok) {
			const res = await response.json();

			const url = res.data.urls[0].url;

			input.value = `<audio controls preload="none"><source src="${url}" type="audio/mpeg"></audio>`;
			sendMessage();
		} else {
			ElMessage.error("Upload failed");
		}
	} catch (err: any) {
		ElMessage.error(err.message);
	}
};

const expandToggle = () => {
	expandS.open = !expandS.open;
};

const sendMessage = () => {
	if (input.value.trim() === "" || disabled.value) return;

	toggle(true);

	let imgTxt = "";
	for (const url of uploadFiles) {
		imgTxt += `<a href="${url.url}" target="_blank" style="cursor: pointer"><img src="${url.url}" width="40" height="40" style="padding-right:8px" /></a>`;
	}

	messages.push({
		text: imgTxt.length ? `<p>${imgTxt}</p>${input.value}` : input.value,
		fromUser: true,
		sessionId,
		parentMessageId: "",
		createTime: today("YYYY.MM.DD HH:mm:ss"),
		rawHtml: ["tts", "images", "whisper"].includes(sessionModelType.value)
	});

	scrollToBottom();

	socket.emit("conversation", {
		content: input.value,
		uploadFiles,
		parentMessageId: parentMessageId.value,
		sessionId,
		model: aiVenderModelVal.value,
		voiceVal: voiceVal.value
	});

	input.value = "";
	uploadFiles.length = 0;
};

const loadSessions = () => {
	service.base.common.chatgpt
		.sessions({
			subject: search.value || "",
			skip: currentPage.value,
			take: pageSize.value
		})
		.then((res: any) => {
			sessions.length = 0;

			res[0].forEach((el: any) => {
				el.isactive = false;
				sessions.push(el);
			});
			total.value = res[1];
		})
		.catch((err: any) => {
			ElMessage.error(err.message);
		});
};

const refreshDetails = () => {
	const session = sessions.find((el) => el.isactive);
	if (session) loadDetails(session);
	ElMessage.success(`已刷新`);
};

const loadDetails = (session: any) => {
	sessions.forEach((el) => {
		el.isactive = false;
	});
	session.isactive = true;

	service.base.common.chatgpt
		.details({
			sessionId: session.id
		})
		.then((res: any) => {
			messages.length = 0;
			subject.value = session.subject;

			aiVenderVal.value = res?.aiVM?.venderId;
			loadAIVenderModel(aiVenderVal.value, res?.aiVM?.id);

			voiceVal.value = session.voiceVal;
			sessionModelType.value = res?.aiVM?.type;

			res.details.forEach((el: any) => {
				let imgText = "";
				for (const url of el.uploads) {
					imgText += `<a href="${url.url}" target="_blank" style="cursor: pointer"><img src="${url.url}" width="40" height="40" style="padding-right: 8px" /></a>`;
				}

				messages.push({
					text: imgText.length ? `<p>${imgText}</p>${el.content}` : el.content,
					fromUser: !el.type,
					sessionId: el.sessionId,
					parentMessageId: el.messageId,
					createTime: el.createTime.replace(/-/g, "."),
					rawHtml: ["tts", "images", "whisper"].includes(sessionModelType.value)
				});

				sessionId = el.sessionId;
				parentMessageId.value = el.messageId;
			});
		})
		.catch((err: any) => {
			ElMessage.error(err.message);
		});
};

const newConversation = () => {
	messages.length = 0;
	input.value = "";
	disabled.value = false;
	sessionId = 0;
	parentMessageId.value = "";
	subject.value = "聊效";

	aiVenderVal.value = 1;
	loadAIVenderModel(aiVenderVal.value);

	sessions.forEach((el) => {
		el.isactive = false;
	});

	ElMessage.success("准备好了，请开始输入！");
	send.value.focus();
};

const scrollToBottom = () => {
	nextTick(() => {
		scrollbarRef.value?.setScrollTop(100000 + Math.random());
	});
};

const loadAIVender = () => {
	service.base.common.chatgpt
		.aiVender()
		.then((res) => {
			aiVender.length = 0;

			for (const el of res) {
				aiVender.push({
					value: el.id,
					label: el.name
				});
			}

			loadAIVenderModel(aiVenderVal.value);
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const loadAIVenderModel = (venderId: any, aiVenderModelSel = 0) => {
	service.base.common.chatgpt
		.aiVenderModel({
			venderId
		})
		.then((res) => {
			aiVenderModel.length = 0;

			for (const el of res) {
				if (el.perm.length === 0) {
					aiVenderModel.push({
						value: el.id,
						label: el.name,
						type: el.type
					});
				} else if (el.perm.length > 0 && checkPerm(el.perm)) {
					aiVenderModel.push({
						value: el.id,
						label: el.name,
						type: el.type
					});
				}
			}

			aiVenderModelSel > 0
				? (aiVenderModelVal.value = aiVenderModelSel)
				: (aiVenderModelVal.value = res[0].id);
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const toggle = (state: boolean) => {
	disabled.value = state;
};

const Form = useForm();

const save = (formEl: FormInstance | undefined) => {
	if (!formEl) return;

	formEl.validate((valid) => {
		if (valid) {
			service.base.common.chatgpt
				.updateSubject({
					id: form.id,
					subject: form.subject,
					digest: form.digest
				})
				.then(() => {
					ElMessage.success(`修改成功`);
					subject.value = form.subject;
					loadSessions();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				})
				.finally(() => {
					dialogFormVisible.value = false;
				});
		} else {
			console.log("error submit!");
			return false;
		}
	});
};

function rowEdit(e: any) {
	dialogFormVisible.value = true;

	form.id = e.id;
	form.subject = e.subject;
	form.digest = e.digest === 1 ? true : false;

	service.base.common.chatgpt
		.tags({
			sessionid: form.id
		})
		.then((res: any) => {
			tags.length = 0;

			let i = 0;
			res.forEach((el: any) => {
				el.type = tagType[i % tagType.length];

				tags.push(el);
				i++;
			});
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
}

function onContextMenu(e: any, { id, subject, digest }: any) {
	if (!id) {
		return false;
	}

	ContextMenu.open(e, {
		list: [
			{
				label: "编辑",
				callback(done) {
					rowEdit({ id, subject, digest });
					done();
				}
			},
			{
				label: "删除",
				callback(done) {
					ElMessageBox.confirm(`此操作将删除【${subject}】, 是否继续?`, "提示", {
						type: "warning"
					})
						.then(() => {
							service.base.common.chatgpt
								.deleteSession({
									id
								})
								.then(() => {
									ElMessage.success("删除成功");

									if (id == sessionId) {
										newConversation();
									}
									loadSessions();
								})
								.catch((err) => {
									ElMessage.error(err.message);
								});
						})
						.catch(() => null);
					done();
				}
			}
		]
	});
}

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	loadSessions();
};
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	loadSessions();
};

const wsSetup = () => {
	const url = isDev ? "http://127.0.0.1:8002" : "https://connect.chasedream.com";

	socket = io(`${url}/ai/chatgpt`, {
		reconnectionDelayMax: 10000,
		auth: {
			token: user.token
		},
		transports: ["websocket"]
	});

	socket.on("sys", (msg: any) => {
		console.log("服务端消息", msg);
	});

	socket.on("conversationErr", (msg: any) => {
		console.log("conversationErr:", msg);

		toggle(false);

		messages.push({
			text: msg,
			fromUser: false,
			sessionId: 0,
			parentMessageId: ""
		});

		for (let session of sessions) {
			if (session.id === sessionId) {
				session.lasMsgText = msg;
				break;
			}
		}

		scrollToBottom();
	});

	socket.on("conversationRes", (res: any) => {
		console.log(res);

		const message = last(messages);

		if (res.sessionModelType === "image") {
			messages.push({
				text: res.imgUrl,
				fromUser: false,
				sessionId,
				parentMessageId: res.id
			});

			parentMessageId.value = res.id;
			if (res.sessionId) sessionId = res.sessionId;

			if (res.isNew) {
				subject.value = res.subject;
				loadSessions();
			}

			toggle(false);
		} else if (res.sessionModelType === "tts") {
			messages.push({
				text: res.url,
				fromUser: false,
				sessionId,
				parentMessageId: res.id,
				rawHtml: true
			});

			parentMessageId.value = res.id;
			if (res.sessionId) sessionId = res.sessionId;

			if (res.isNew) {
				subject.value = res.subject;
				loadSessions();
			}

			toggle(false);
		} else if (res.sessionModelType === "whisper") {
			messages.push({
				text: res.text,
				fromUser: false,
				sessionId,
				parentMessageId: res.id,
				rawHtml: true
			});

			parentMessageId.value = res.id;
			if (res.sessionId) sessionId = res.sessionId;

			if (res.isNew) {
				subject.value = res.subject;
				loadSessions();
			} else {
				for (let session of sessions) {
					if (session.id === sessionId && res?.content?.length) {
						session.lasMsgText = res.content;
						break;
					}
				}
			}

			toggle(false);
		} else {
			if (message.fromUser) {
				messages.push({
					text: res?.content || "",
					fromUser: false,
					sessionId,
					parentMessageId: res.id
				});
			} else {
				message.text += res?.content || "";
				toggle(false);
			}

			if (res?.response_metadata?.finish_reason !== null) {
				parentMessageId.value = res.id;
				if (res.sessionId) sessionId = res.sessionId;

				if (res.isNew) {
					subject.value = res.subject;
					loadSessions();
				} else {
					for (let session of sessions) {
						if (session.id === sessionId && res?.content?.length) {
							session.lasMsgText = res.content;
							break;
						}
					}
				}

				toggle(false);
			}
		}

		scrollToBottom();
	});

	socket.on("connect_error", () => {
		setTimeout(() => {
			socket.connect();
		}, 3000);
	});

	socket.on("connect", () => {
		console.log(`connect: ${socket.id}`);
	});

	socket.on("disconnect", () => {
		console.log(`disconnect`);
	});
};

const initInputFull = () => {
	const input = send.value.$el.querySelector("textarea");
	const container = input.parentElement.parentElement.parentElement;
	const containerH = container.getBoundingClientRect().height;

	const lineHeight = parseInt(getComputedStyle(input).lineHeight);
	const maxRows = containerH / lineHeight;

	rows.value = maxRows - 1;
	inputHeight.value = `${rows.value * lineHeight - 20}px`;
};

onMounted(() => {
	initInputFull();
	loadSessions();
	loadAIVender();
	wsSetup();

	const gpt4 = checkPerm(service.base.common.chatgpt.permission.chatgpt4);
	if (gpt4) aiVenderModelVal.value = 1;
});
</script>

<style lang="scss" scoped>
@import "../css/index.scss";
</style>
