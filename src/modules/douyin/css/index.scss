.view-douyin {
	display: flex;
	background-color: #fff;
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	.wrapper {
		margin: 10px 5px 10px 5px;
		display: flex;
		flex-direction: column;
		width: 100%;
		height: 100%;
		.opt {
			margin-bottom: 10px;
		}
		.list {
			display: flex;
			height: calc(100% - 70px);
			overflow: hidden;
			flex: 1;
		}
		.page {
			display: flex;
			height: 70px;
			padding-bottom: 10px;
			justify-content: right;
		}
	}
	.infinite-list {
		display: flex;
		flex-direction: row;
    	flex-wrap: wrap;
    	justify-content: center;
		width: 100%;
		height: 450px;
		padding: 0;
		margin: 0;
	  }
	  .infinite-list-item {
		display: flex;
		flex-wrap: wrap;
		align-content: center;
		justify-content: center;
		flex-direction: row;
		width: 130px;
		height: 215px;
		margin: 5px;
		overflow: hidden;
		position: relative;
		.desc {
			width: 130px;
			height: 24px;
			font-size: 12px;
			line-height: 16px;
			padding-top: 5px;
		}
		.box {
			position: absolute;
			top: 10px;
			right: 5px;
			z-index: 99;
		}
	  }
	  .loading {
		margin: 0 auto;
	  }
	.scan {
		display: flex;
    	justify-content: center;
	}
}

.cursor-pointer {
	cursor: pointer;
}

.ellipsis-tooltip {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}