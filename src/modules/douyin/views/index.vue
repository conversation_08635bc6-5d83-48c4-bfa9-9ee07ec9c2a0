<template>
	<div class="view-douyin">
		<div class="wrapper">
			<el-row class="opt">
				<el-button type="primary" @click="openDialog(1)">新增</el-button>
				<el-button @click="refresh">刷新</el-button>
			</el-row>
			<el-row class="list">
				<el-table
					border
					:data="tableData"
					:max-height="tableHeight"
					:row-style="{ height: '40px' }"
					:header-cell-style="{ background: '#ebeef5', color: '#333' }"
				>
					<el-table-column fixed prop="id" label="ID" width="70" align="center">
						<template #header>
							<el-input
								v-model="search.id"
								size="small"
								placeholder="ID"
								clearable
								@change="refresh"
							/>
						</template>
					</el-table-column>
					<el-table-column
						prop="author"
						label="作者"
						width="120"
						:show-overflow-tooltip="true"
						align="left"
					>
						<template #header>
							<el-input
								v-model="search.author"
								size="small"
								placeholder="作者"
								clearable
								@change="refresh"
							/>
						</template>
					</el-table-column>
					<el-table-column
						prop="title"
						label="描述"
						:show-overflow-tooltip="true"
						align="left"
					>
						<template #header>
							<el-input
								v-model="search.title"
								size="small"
								placeholder="描述"
								clearable
								@change="refresh"
							/>
						</template>
					</el-table-column>
					<el-table-column
						prop="message"
						label="错误信息"
						width="120"
						:show-overflow-tooltip="true"
						align="center"
					>
					</el-table-column>
					<el-table-column label="链接/视频" width="110" align="center">
						<template #default="scope">
							<img
								v-if="scope.row.targetUrl.length"
								src="/@/assets/douyin.svg"
								@click.stop="openClick(scope.row)"
								class="cursor-pointer"
								style="width: 20px; height: 20px; margin-right: 8px"
							/>
							<el-icon
								v-if="scope.row.targetUrl.length"
								size="20"
								class="cursor-pointer"
								style="color: var(--color-primary); margin-right: 8px"
								@click.stop="copyClick(scope.row, 1)"
								><document-copy
							/></el-icon>
							<el-icon
								v-if="scope.row.targetUrl.length"
								size="20"
								class="cursor-pointer"
								style="color: #00d06c"
								@click.stop="downClick(scope.row)"
								><Download
							/></el-icon>
						</template>
					</el-table-column>
					<el-table-column label="文本" width="60" align="center">
						<template #default="scope">
							<el-icon
								v-if="scope.row.txt.length"
								size="20"
								class="cursor-pointer"
								style="color: var(--color-primary)"
								@click.stop="txtClick(scope.row)"
								><document-copy
							/></el-icon>
						</template>
					</el-table-column>
					<el-table-column label="状态" width="80" align="center">
						<template #default="scope">
							{{ statusTxt(scope.row) }}
						</template>
					</el-table-column>
					<el-table-column
						prop="videoDate"
						label="发布时间"
						width="100"
						align="center"
						:formatter="formatter"
					/>
					<el-table-column fixed="right" label="操作" width="100" align="center">
						<template #default="scope">
							<el-icon
								@click="deleteHandler(scope.row.id)"
								class="cursor-pointer"
								style="color: crimson; margin-right: 8px"
								:size="20"
								><delete
							/></el-icon>
							<el-icon
								v-if="scope.row.message.length"
								@click="redo(scope.row)"
								class="cursor-pointer"
								style="color: cadetblue"
								:size="20"
								><refresh-right
							/></el-icon>
						</template>
					</el-table-column>
				</el-table>
			</el-row>
			<el-row class="page">
				<el-pagination
					class="pagination"
					:hide-on-single-page="true"
					background
					:currentPage="currentPage"
					:page-size="pageSize"
					layout="total, sizes, prev, pager, next, jumper"
					:total="total"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				/>
			</el-row>
			<cl-dialog :title="title" v-model="visible" @closed="resetForm">
				<el-form :model="form" :rules="rules" ref="formRef" status-icon>
					<el-form-item label="Url" prop="url">
						<el-input v-model="form.url" placeholder="Url" type="textarea" :rows="5">
						</el-input>
					</el-form-item>

					<el-form-item>
						<cl-flex1 />
						<el-button @click="closeDialog">取消</el-button>
						<el-button
							v-if="form.aweme_detail.aweme_id"
							type="primary"
							@click="downloadVideo"
							>下载视频</el-button
						>
						<el-button
							v-if="form.aweme_detail.aweme_id"
							type="primary"
							@click="downloadCover"
							>下载封面</el-button
						>
						<el-button type="success" @click="submitForm(formRef)">上传</el-button>
						<el-button type="primary" @click="clear">清空</el-button>
						<el-button type="primary" :loading="form.btnLoading" @click="crawl"
							>解析</el-button
						>
					</el-form-item>
				</el-form>
			</cl-dialog>

			<el-dialog
				v-model="scanVisible"
				title="扫码登录"
				width="30%"
				center
				@closed="closeScan"
			>
				<el-image :src="scan.image" class="scan" />
			</el-dialog>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { DocumentCopy, Delete, RefreshRight, Download } from "@element-plus/icons-vue";
import { ref, reactive, onMounted, nextTick, computed, toRaw } from "vue";
import { useCool } from "/@/cool";
import { removeEmptyFromObject } from "/@/cool/utils";
import { useClipboard } from "@vueuse/core";
import { datelineToDate } from "/@/cool/utils";

const { service } = useCool();
const { copy } = useClipboard();

const search = reactive({
	id: "",
	url: "",
	author: "",
	title: "",
	targetUrl: ""
});

const formRef = ref<FormInstance>();
const form = reactive({
	id: 0,
	type: "1",
	url: "",
	author: "",
	title: "",
	targetUrl: "",
	btnLoading: false,
	aweme_detail: {}
});

const tableHeight = ref(0);
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);
const visible = ref(false);
const status = ref(0);
const scanVisible = ref(false);
const scan = reactive<any>({});

const title = computed(() => {
	return status.value === 1 ? "新增" : "编辑";
});

const formatter = (row: any) => {
	return datelineToDate(row.videoDate, "YYYY-MM-DD");
};

const redo = (row: any) => {
	service.base.common.douyin
		.urlRedo({
			id: row.id
		})
		.then((res) => {
			row.message = "";
			row.status = 0;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const clear = () => {
	form.url = "";
};

const crawl = () => {
	if (form.url.length <= 0) return;

	form.aweme_detail = {};
	form.btnLoading = true;

	service.base.common.douyin
		.douyinUrlParse({
			url: form.url
		})
		.then((res: any) => {
			if (res.aweme_detail) {
				form.aweme_detail = res.aweme_detail;
				form.aweme_detail.type = res.type;
			} else {
				ElMessage.error(res?.filter_detail?.detail_msg || "解析失败");
			}
		})
		.catch((err) => {
			ElMessage.error(err.message);
		})
		.finally(() => {
			form.btnLoading = false;
		});
};

const downloadVideo = () => {
	let url = "";
	const aweme_detail = toRaw(form.aweme_detail);

	if (aweme_detail.type === "ixigua") {
		url = aweme_detail.video.play_addr.url_list[0];
	} else {
		url = aweme_detail.realUrl;
	}

	service.base.common.douyin
		.douyinUrlDownload({
			url
		})
		.then((res) => {
			const uint8Array = new Uint8Array(res.file.data);

			const link = document.createElement("a");
			link.href = window.URL.createObjectURL(new Blob([uint8Array]));
			link.setAttribute("download", `${aweme_detail.aweme_id}.mp4`);
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const downloadCover = () => {
	let url = "";
	const aweme_detail = toRaw(form.aweme_detail);

	if (aweme_detail.type === "ixigua") {
		url = aweme_detail.video.cover.url_list[0];
	} else {
		url = aweme_detail.video.cover.url_list.find((el: string) => el.includes(".jpeg"));
	}

	service.base.common.douyin
		.douyinUrlDownload({
			url
		})
		.then((res) => {
			const uint8Array = new Uint8Array(res.file.data);

			const link = document.createElement("a");
			link.href = window.URL.createObjectURL(new Blob([uint8Array]));
			link.setAttribute("download", `${aweme_detail.aweme_id}.jpeg`);
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.douyin
		.urlPage({
			...s,
			page: currentPage.value,
			size: pageSize.value
		})
		.then((res) => {
			tableData.value = res?.list;
			total.value = res?.pagination?.total;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const closeScan = () => {
	clearInterval(scan.interval);
	scanVisible.value = false;
};

refresh();

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};

const openDialog = (val: any) => {
	status.value = val;
	visible.value = true;
};

const closeDialog = () => {
	status.value = 0;
	visible.value = false;

	resetForm();
};

const resetForm = () => {
	form.id = 0;
	form.url = "";
	form.author = "";
	form.aweme_detail = {};
};

const deleteHandler = (id: number) => {
	ElMessageBox.confirm("此操作将永久删除选中数据，是否继续？", {
		title: "提示",
		confirmButtonText: "确定",
		cancelButtonText: "取消",
		type: "warning"
	})
		.then(() => {
			service.base.common.douyin
				.urlDelete({
					id
				})
				.then((res) => {
					ElMessage({
						message: "删除成功",
						type: "success"
					});
					refresh();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		})
		.catch(() => {});
};

const rules = reactive<FormRules>({
	type: [
		{
			required: true,
			message: "请选择类型",
			trigger: "blur"
		}
	],
	url: [{ required: true, message: "请输入Url", trigger: "blur" }]
});

const submitForm = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate((valid, fields) => {
		if (!valid) return;

		service.base.common.douyin
			.urlCreate({
				...form
			})
			.then((res) => {
				ElMessage({
					message: "新增成功",
					type: "success"
				});
				closeDialog();
				resetForm();
				refresh();
			})
			.catch((err) => {
				ElMessage.error(err.message);
			});
	});
};

const openClick = (row: any) => {
	window.open(row.url);
};

const copyClick = (row: any, type: number) => {
	let url = "";

	if (type === 1) {
		url = `https://connect.chasedream.com${row.localFile}`;
	} else if (type === 2) {
		url = row.remoteFileUrl;
	}
	copy(url);

	ElMessage({
		message: "已复制",
		type: "success"
	});
};

const downClick = (row: any) => {
	const fileName = row.localFile.substring(row.localFile.lastIndexOf("/") + 1);

	service.base.common.douyin
		.douyinUrlDownload({
			url: `https://connect.chasedream.com${row.localFile}`
		})
		.then((res) => {
			const uint8Array = new Uint8Array(res.file.data);

			const link = document.createElement("a");
			link.href = window.URL.createObjectURL(new Blob([uint8Array]));
			link.setAttribute("download", fileName);
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const txtClick = (row: any) => {
	copy(row.txt);

	ElMessage({
		message: "已复制",
		type: "success"
	});
};

const statusTxt = (row: any) => {
	let result = "";
	if (row.status === 0) {
		result = "待处理";
	} else if (row.status === 1) {
		result = "处理中";
	} else if (row.status === 2) {
		result = "已处理";
	} else if (row.status === -1) {
		result = "听悟转换异常";
	} else if (row.status === -2) {
		result = "抖音Url解析异常";
	}

	return result;
};

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 230;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 230;
		};
	});
});
</script>

<style lang="scss" scoped>
@import "../css/index.scss";
</style>
