<template>
	<div class="view-douyin">
		<div class="wrapper">
			<el-row class="opt">
				<el-button type="primary" @click="openDialog(1)">新增</el-button>
				<el-button @click="refresh">刷新</el-button>
			</el-row>
			<el-row class="list">
				<el-table
					border
					:data="tableData"
					:max-height="tableHeight"
					:row-style="{ height: '40px' }"
					:header-cell-style="{ background: '#ebeef5', color: '#333' }"
				>
					<el-table-column fixed prop="id" label="ID" width="70" align="center">
						<template #header>
							<el-input
								v-model="search.id"
								size="small"
								placeholder="ID"
								clearable
								@change="refresh"
							/>
						</template>
					</el-table-column>
					<el-table-column
						prop="username"
						label="账号"
						:show-overflow-tooltip="true"
						align="left"
					>
						<template #header>
							<el-input
								v-model="search.username"
								size="small"
								placeholder="账号"
								clearable
								@change="refresh"
							/>
						</template>
					</el-table-column>
					<el-table-column label="主账号" width="80" align="center">
						<template #default="scope">
							<el-switch
								v-model="scope.row.master"
								:active-value="1"
								:inactive-value="0"
								@change="masterHandler(scope.row)"
							/>
						</template>
					</el-table-column>
					<el-table-column label="状态" width="80" align="center">
						<template #default="scope">
							<el-switch
								v-model="scope.row.status"
								:active-value="1"
								:inactive-value="0"
								@change="switchHandler(scope.row)"
							/>
						</template>
					</el-table-column>
					<el-table-column
						prop="exp"
						label="过期时间"
						width="100"
						align="center"
						:formatter="formatter"
					/>
					<el-table-column
						prop="updateTime"
						label="更新时间"
						width="100"
						align="center"
					/>
					<el-table-column fixed="right" label="操作" width="120" align="center">
						<template #default="scope">
							<el-icon
								@click="editClick(scope.row)"
								class="cursor-pointer"
								style="color: #464bd7; margin-right: 8px"
								:size="20"
								><Edit
							/></el-icon>
							<el-icon
								@click="scanClick(scope.row)"
								class="cursor-pointer"
								style="color: #67c23a; margin-right: 8px"
								:size="20"
								><Refresh
							/></el-icon>
							<el-icon
								@click="deleteHandler(scope.row.id)"
								class="cursor-pointer"
								style="color: crimson; margin-right: 8px"
								:size="20"
								><delete
							/></el-icon>
						</template>
					</el-table-column>
				</el-table>
			</el-row>

			<cl-dialog :title="title" v-model="visible" @closed="resetForm">
				<el-form :model="form" :rules="rules" ref="formRef" status-icon>
					<el-form-item label="账号" prop="username">
						<el-input v-model="form.username" placeholder="账号"> </el-input>
					</el-form-item>

					<el-form-item>
						<cl-flex1 />
						<el-button @click="closeDialog">取消</el-button>
						<el-button type="success" @click="submitForm(formRef)">保存</el-button>
					</el-form-item>
				</el-form>
			</cl-dialog>

			<el-dialog
				v-model="scanVisible"
				title="扫码登录"
				width="30%"
				center
				@closed="closeScan"
			>
				<el-image :src="scan.image" class="scan" />
			</el-dialog>

			<cl-dialog title="编辑Cookie" v-model="cookievisible" @closed="resetCookieForm">
				<el-form :model="cookieForm" ref="cookieFormRef" status-icon>
					<el-form-item label="Cookie" prop="cookie">
						<el-input
							v-model="cookieForm.cookie"
							placeholder="Cookie"
							:rows="5"
							type="textarea"
						>
						</el-input>
					</el-form-item>

					<el-form-item>
						<cl-flex1 />
						<el-button @click="closeCookieDialog">取消</el-button>
						<el-button type="success" @click="submitCookieForm()">保存</el-button>
					</el-form-item>
				</el-form>
			</cl-dialog>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { Refresh, Edit, Delete } from "@element-plus/icons-vue";
import { ref, reactive, onMounted, nextTick, computed } from "vue";
import { useCool } from "/@/cool";
import { removeEmptyFromObject } from "/@/cool/utils";
import { datelineToDate } from "/@/cool/utils";

const { service } = useCool();

const search = reactive({
	id: "",
	username: ""
});

const formRef = ref<FormInstance>();
const form = reactive({
	id: 0,
	username: ""
});

const cookieFormRef = ref<FormInstance>();
const cookieForm = reactive({
	id: 0,
	cookie: ""
});

const tableHeight = ref(0);
const tableData = ref([]);

const visible = ref(false);
const cookievisible = ref(false);
const status = ref(0);
const scanVisible = ref(false);
const scan = reactive<any>({});

const title = computed(() => {
	return status.value === 1 ? "新增" : "编辑";
});

const masterHandler = (row: any) => {
	service.base.common.douyin
		.accountSwitch({
			id: row.id
		})
		.then((res) => {
			ElMessage({
				message: "修改成功",
				type: "success"
			});
			refresh();
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const switchHandler = (row: any) => {
	service.base.common.douyin
		.accountStatus({
			id: row.id,
			status: row.status
		})
		.then((res) => {
			ElMessage({
				message: "修改成功",
				type: "success"
			});
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const formatter = (row: any) => {
	return datelineToDate(row.exp, "YYYY-MM-DD");
};

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.douyin
		.account({
			...s
		})
		.then((res) => {
			tableData.value = res;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const closeScan = () => {
	clearInterval(scan.interval);
	scanVisible.value = false;
};

const editClick = (row: any) => {
	cookieForm.id = row.id;
	cookieForm.cookie = row.cookie;

	cookievisible.value = true;
};

const scanClick = (row: any) => {
	scan.interval = setInterval(() => {
		if (!scan.token || !scan.verifyFp) return;

		service.base.common.douyin
			.checkQrCode({
				token: scan.token,
				verifyFp: scan.verifyFp,
				cookie: scan.cookie
			})
			.then((res) => {
				console.log(res);
				// status字段
				// 1 二维码未失效，请扫码！
				// 2 已扫码，请确认！
				// 3 已确认，登录成功！
				// 5 二维码已失效，请重新运行！

				if (res.data?.status == "3") {
					clearInterval(scan.interval);
					scanVisible.value = false;
					scan.cookie = res.cookie;

					service.base.common.douyin
						.loginQrCode({
							id: row.id,
							redirect_url: res.data.redirect_url,
							cookie: scan.cookie
						})
						.then((res) => {
							ElMessage({
								message: "扫码成功",
								type: "success"
							});
						})
						.catch((err) => {
							ElMessage.error(err.message);
						});
				}
			});
	}, 1000);

	service.base.common.douyin
		.qrCodeUrl()
		.then((res) => {
			scan.image = res.image;
			scan.token = res.token;
			scan.verifyFp = res.verifyFp;
			scan.cookie = res.cookie;

			scanVisible.value = true;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

refresh();

const openDialog = (val: any) => {
	status.value = val;
	visible.value = true;
};

const closeDialog = () => {
	status.value = 0;
	visible.value = false;

	resetForm();
};

const resetForm = () => {
	form.id = 0;
	form.username = "";
};

const closeCookieDialog = () => {
	cookievisible.value = false;

	resetCookieForm();
};

const resetCookieForm = () => {
	cookieForm.id = 0;
	cookieForm.cookie = "";
};

const deleteHandler = (id: number) => {
	ElMessageBox.confirm("此操作将永久删除选中数据，是否继续？", {
		title: "提示",
		confirmButtonText: "确定",
		cancelButtonText: "取消",
		type: "warning"
	})
		.then(() => {
			service.base.common.douyin
				.accountDelete({
					id
				})
				.then((res) => {
					ElMessage({
						message: "删除成功",
						type: "success"
					});
					refresh();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		})
		.catch(() => {});
};

const rules = reactive<FormRules>({
	username: [{ required: true, message: "请输入账号", trigger: "blur" }]
});

const submitForm = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate((valid, fields) => {
		if (!valid) return;

		service.base.common.douyin
			.accountCreate({
				...form
			})
			.then((res) => {
				ElMessage({
					message: "新增成功",
					type: "success"
				});
				closeDialog();
				resetForm();
				refresh();
			})
			.catch((err) => {
				ElMessage.error(err.message);
			});
	});
};

const submitCookieForm = () => {
	service.base.common.douyin
		.accountCookie({
			...cookieForm
		})
		.then((res) => {
			ElMessage({
				message: "修改成功",
				type: "success"
			});
			closeCookieDialog();
			resetCookieForm();
			refresh();
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 230;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 230;
		};
	});
});
</script>

<style lang="scss" scoped>
@import "../css/index.scss";
</style>
