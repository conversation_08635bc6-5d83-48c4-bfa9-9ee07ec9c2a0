<template>
	<div class="wrapper">
		<el-row class="opt">
			<el-col :span="12">
				<el-button @click="addClick" type="primary">添加</el-button>
			</el-col>
			<el-col :span="12" style="text-align: right">
				<el-input
					placeholder="请输入关键字"
					v-model="search.s"
					style="width: 300px"
					clearable
					@clear="refresh"
				>
					<template #append>
						<el-button :icon="Search" @click="refresh" />
					</template>
				</el-input>
			</el-col>
		</el-row>
		<el-row class="list">
			<el-col :span="24">
				<el-table
					border
					style="width: 100%"
					:data="tableData"
					:max-height="tableHeight"
					:row-style="{ height: '40px' }"
					:header-cell-style="{ background: '#ebeef5', color: '#333' }"
				>
					<el-table-column fixed prop="id" label="ID" width="70" align="center">
					</el-table-column>
					<el-table-column label="显示全称/显示简称" align="left">
						<template #default="scope">
							{{ scope.row.school_name }} <br />
							{{ scope.row.university_name }}
						</template>
					</el-table-column>
					<el-table-column width="110" label="官网" align="center">
						<template #default="scope">
							<a :href="scope.row.website" target="_blank">官网</a>
						</template>
					</el-table-column>
					<el-table-column width="110" label="地区/国家" align="center">
						<template #default="scope">
							{{ scope.row.area }} <br />
							{{ scope.row.country }}
						</template>
					</el-table-column>
					<el-table-column fixed="right" label="操作" width="100" align="center">
						<template #default="scope">
							<el-icon
								@click="editClick(scope.row)"
								class="cursor-pointer"
								style="color: #464bd7; margin-right: 8px"
								:size="20"
								><edit
							/></el-icon>
						</template>
					</el-table-column>
				</el-table>
			</el-col>
		</el-row>
		<el-row class="page">
			<el-pagination
				class="pagination"
				background
				:currentPage="currentPage"
				:page-size="pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</el-row>

		<el-dialog v-model="visible" :title="title" width="800">
			<el-form
				:model="form"
				label-width="120px"
				style="max-width: 800px"
				ref="addRef"
				:rules="rules"
			>
				<el-form-item label="文件目录" :required="true" prop="directory">
					<el-input v-model="form.directory" placeholder="请输入文件目录" />
				</el-form-item>
				<el-form-item label="商学院名称（英）" :required="true" prop="school_name">
					<el-input v-model="form.school_name" placeholder="请输入商学院名称（英）" />
				</el-form-item>
				<el-form-item label="商学院名称（中）" :required="true" prop="school_name_cn">
					<el-input v-model="form.school_name_cn" placeholder="请输入商学院名称（中）" />
				</el-form-item>
				<el-form-item label="大学名称（英）" :required="true" prop="university_name">
					<el-input v-model="form.university_name" placeholder="请输入大学名称（英）" />
				</el-form-item>
				<el-form-item label="大学名称（中）" :required="true" prop="university_name_cn">
					<el-input
						v-model="form.university_name_cn"
						placeholder="请输入大学名称（中）"
					/>
				</el-form-item>
				<el-form-item label="显示全称" :required="true" prop="display_name">
					<el-input v-model="form.display_name" placeholder="请输入显示全称" />
				</el-form-item>
				<el-form-item label="显示简称" :required="true" prop="short">
					<el-input v-model="form.short" placeholder="请输入显示简称" />
				</el-form-item>
				<el-form-item label="LOGO" :required="true" prop="logo_url">
					<div v-if="form.logo_url?.length > 0" class="material">
						<el-image
							style="width: 100px; height: 100px"
							:src="form.logo_url"
							fit="cover"
						/>
						<el-icon class="del" size="20" @click="delMaterial"><delete /></el-icon>
					</div>
					<el-icon
						v-else
						class="cursor-pointer"
						size="30"
						color="#CFD3DC"
						@click="addMaterial"
						><circle-plus
					/></el-icon>
					<input
						type="file"
						multiple
						accept="image/*"
						ref="fileInput"
						style="display: none"
						@change="handleFileChange($event)"
					/>
				</el-form-item>
				<el-form-item label="关键词" :required="true" prop="keyword">
					<el-input v-model="form.keyword" placeholder="请输入关键词" />
				</el-form-item>
				<el-form-item label="商学院首页" :required="true" prop="website">
					<el-input v-model="form.website" placeholder="请输入商学院首页" />
				</el-form-item>
				<el-form-item label="所属地区" :required="true" prop="area">
					<el-select v-model="form.area" placeholder="请选择地区">
						<el-option label="北美洲" value="北美洲" />
						<el-option label="南美洲" value="南美洲" />
						<el-option label="亚洲" value="亚洲" />
						<el-option label="欧洲" value="欧洲" />
						<el-option label="大洋洲" value="大洋洲" />
						<el-option label="非洲" value="非洲" />
					</el-select>
				</el-form-item>
				<el-form-item label="所属国家" :required="true" prop="country">
					<el-select v-model="form.country" placeholder="请选择国家">
						<el-option
							v-for="country in countries"
							:key="country.id"
							:label="country.name"
							:value="country.name"
						/>
					</el-select>
				</el-form-item>

				<div style="margin-left: 50px; margin-bottom: 5px; font-size: 18px">专业</div>

				<div v-for="(obj, index) in form.majors" :key="index" class="locationWrap">
					<div class="location-header" v-if="obj.id == 0">
						<el-icon
							class="cursor-pointer delete-icon"
							size="20"
							color="crimson"
							@click="removeMajor(index)"
							><delete
						/></el-icon>
					</div>

					<el-form-item label="专业全称（英）" :required="true">
						<el-input
							v-model="obj.fullname_english"
							placeholder="请输入专业全称（英）"
							style="width: 300px"
						/>
					</el-form-item>
					<el-form-item label="专业全称（中）" :required="true">
						<el-input
							v-model="obj.fullname_chinese"
							placeholder="请输入专业全称（中）"
							style="width: 300px"
						/>
					</el-form-item>
					<el-form-item label="专业简称" :required="true">
						<el-input
							v-model="obj.short"
							placeholder="请输入专业简称"
							style="width: 300px"
						/>
					</el-form-item>
					<el-form-item label="所属分类" :required="true">
						<div
							v-for="item in majors"
							:key="item.g"
							style="position: relative; width: 100%"
						>
							<span
								style="padding-right: 10px; position: absolute; left: 0; top: 0"
								>{{ item.g }}</span
							>
							<el-radio-group v-model="obj.major_id" style="padding-left: 50px">
								<el-radio
									:label="opt.id"
									v-for="(opt, idx) in item.names"
									:key="idx"
									>{{ opt.display_name }}</el-radio
								>
							</el-radio-group>
						</div>
					</el-form-item>
				</div>

				<el-form-item>
					<el-icon class="cursor-pointer" size="30" color="#CFD3DC" @click="addMajor"
						><circle-plus
					/></el-icon>
				</el-form-item>

				<el-form-item>
					<div class="form-submit">
						<el-button type="primary" @click="submitForm(addRef)">提交</el-button>
						<el-button @click="visible = false">取消</el-button>
					</div>
				</el-form-item>
			</el-form>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from "element-plus";
import { Edit, Delete, Search, CirclePlus } from "@element-plus/icons-vue";
import { ref, reactive, onMounted, nextTick, onUnmounted, computed } from "vue";
import type { FormInstance } from "element-plus";
import { useCool } from "/@/cool";
import { removeEmptyFromObject } from "/@/cool/utils";
import { isDev } from "/@/config";

const { service } = useCool();

const search = reactive({
	id: "",
	s: ""
});

const tableHeight = ref(0);
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(50);
const total = ref(0);

const title = ref("添加");
const visible = ref(false);
const addRef = ref<FormInstance>();
const form = reactive({
	id: 0,
	directory: "",
	school_name: "",
	school_name_cn: "",
	university_name: "",
	university_name_cn: "",
	display_name: "",
	short: "",
	logo_url: "",
	keyword: "",
	website: "",
	area: "",
	country: "",
	majors: [
		{
			id: 0,
			fullname_english: "",
			fullname_chinese: "",
			short: "",
			major_id: ""
		}
	]
});

const countries = ref([]);
const majors = ref([]);
const fileInput = ref(null);

const rules = reactive({
	directory: [{ required: true, message: "请输入文件目录", trigger: "blur" }],
	school_name: [{ required: true, message: "请输入商学院名称（英）", trigger: "blur" }],
	school_name_cn: [{ required: true, message: "请输入商学院名称（中）", trigger: "blur" }],
	university_name: [{ required: true, message: "请输入大学名称（英）", trigger: "blur" }],
	university_name_cn: [{ required: true, message: "请输入大学名称（中）", trigger: "blur" }],
	display_name: [{ required: true, message: "请输入显示全称", trigger: "blur" }],
	short: [{ required: true, message: "请输入显示简称", trigger: "blur" }],
	logo_url: [{ required: true, message: "请输入LOGO", trigger: "blur" }],
	keyword: [{ required: true, message: "请输入关键词", trigger: "blur" }],
	website: [{ required: true, message: "请输入商学院首页", trigger: "blur" }],
	area: [{ required: true, message: "请选择地区", trigger: "blur" }],
	country: [{ required: true, message: "请选择国家", trigger: "blur" }]
});

const addMaterial = () => {
	fileInput.value?.click();
};

const delMaterial = async () => {
	form.logo_url = "";
};

const handleFileChange = async (event: any) => {
	const files = event.target.files;
	if (files) {
		await upload(files);
	}

	event.target.value = "";
};

const upload = async (files: any) => {
	const formData = new FormData();

	for (let i = 0; i < files.length; i++) {
		formData.append("files", files[i]);
	}

	const baseUrl = isDev
		? "http://localhost:9000/dev/admin/base/open/upload4EventSchoolLogo"
		: "https://connect.chasedream.com/api/v2/admin/base/open/upload4EventSchoolLogo";

	const params = new URLSearchParams({
		directory: form.directory
	});

	const url = `${baseUrl}?${params.toString()}`;

	const response = await fetch(url, {
		method: "POST",
		body: formData
	});

	if (response.ok) {
		const res = await response.json();

		if (res.code === 1001) {
			ElMessage.error(res.message);
			return;
		}

		form.logo_url = res.data.image + `?ts=${+new Date()}`;
	}
};

const submitForm = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	let valid = true;
	let fieldsError: any = [];

	await formEl.validate((valid, fields) => {
		if (!valid) return;

		form.majors.forEach((obj, index) => {
			if (obj.fullname_english.length === 0) {
				valid = false;
				fieldsError.push({
					index,
					field: "fullname_english",
					message: "请选择专业全称（英）"
				});
			}
			if (obj.fullname_chinese.length === 0) {
				valid = false;
				fieldsError.push({
					index,
					field: "fullname_chinese",
					message: "请选择专业全称（中）"
				});
			}
			if (obj.short.length === 0) {
				valid = false;
				fieldsError.push({ index, field: "short", message: "请选择专业简称" });
			}
			if (obj.major_id.length === 0) {
				valid = false;
				fieldsError.push({ index, field: "major_id", message: "请选择所属分类" });
			}
		});

		if (!valid) {
			fieldsError.forEach((error) => {
				ElMessage.error(`${error.message}`);
			});
			return;
		}

		form.logo_url = form.logo_url.split("?")[0];

		service.base.common.event
			.schoolCreateAndUpdate({
				...form
			})
			.then((res) => {
				ElMessage({
					message: "已提交!",
					type: "success"
				});

				visible.value = false;
				refresh();
			})
			.catch((err) => {
				ElMessage.error(err.message);
			});
	});
};

const reset = () => {
	title.value = "";

	form.id = 0;
	form.directory = "";
	form.school_name = "";
	form.school_name_cn = "";
	form.university_name = "";
	form.university_name_cn = "";
	form.display_name = "";
	form.short = "";
	form.logo_url = "";
	form.keyword = "";
	form.website = "";
	form.area = "";
	form.country = "";
	form.majors.length = 0;

	form.majors.push({
		id: 0,
		fullname_english: "",
		fullname_chinese: "",
		short: "",
		major_id: ""
	});
};

const removeMajor = (index) => {
	form.majors.splice(index, 1);
};

const addMajor = () => {
	form.majors.push({
		id: 0,
		fullname_english: "",
		fullname_chinese: "",
		short: "",
		major_id: ""
	});
};

const addClick = () => {
	reset();

	title.value = "添加";
	visible.value = true;
};

const editClick = (row: any) => {
	reset();

	title.value = "编辑";
	visible.value = true;

	Object.assign(form, row);
};

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.event
		.schoolList({
			...s,
			page: currentPage.value,
			pageSize: pageSize.value
		})
		.then((res) => {
			tableData.value = res[0];
			total.value = res[1];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const loadMajors = () => {
	service.base.common.event
		.majorCategory()
		.then((res) => {
			majors.value.length = 0;
			majors.value.push(...res);
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const loadGeo = (parent_id = "", target = "") => {
	service.base.common.event
		.geo({ parent_id })
		.then((res) => {
			if (target === "countries") {
				countries.value = res;
			} else if (target === "regions") {
				// regions.value = res;
			} else if (target === "cities") {
				// cities.value = res;
			}
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

refresh();

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 250;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 250;
		};
	});

	loadGeo("", "countries");
	loadMajors();
});

onUnmounted(() => {
	reset();
});
</script>

<style lang="scss" scoped>
@import "../css/index.scss";
</style>
