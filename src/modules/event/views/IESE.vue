<template>
	<div class="wrapper">
		<el-row class="opt">
			<el-col :span="4">
				<el-button type="primary" @click="add">添加</el-button>
			</el-col>
		</el-row>
		<el-row class="list">
			<el-col :span="24">
				<el-tabs v-model="activeTab">
					<el-tab-pane label="当前活动" name="current">
						<el-table
							border
							style="width: 100%"
							:data="tableData"
							:max-height="tableHeight"
							:row-style="{ height: '40px' }"
							:header-cell-style="{ background: '#ebeef5', color: '#333' }"
						>
							<el-table-column fixed prop="id" label="ID" width="70" align="center">
							</el-table-column>
							<el-table-column prop="subject" label="标题" :show-overflow-tooltip="true">
							</el-table-column>
							<el-table-column prop="event_geo.name" label="地点" width="90" align="center">
							</el-table-column>
							<el-table-column label="展示/开始结束时间" width="180" align="center">
								<template #default="scope">
									{{ datelineToDate(scope.row.push_begin_date) }}
									{{ datelineToDate(scope.row.push_end_date) }}
								</template>
							</el-table-column>
							<el-table-column label="活动/开始结束时间	" width="180" align="center">
								<template #default="scope">
									{{ datelineToDate(scope.row.event_begin_date) }}
									{{ datelineToDate(scope.row.event_end_date) }}
								</template>
							</el-table-column>
							<el-table-column label="创建时间" width="180" align="center">
								<template #default="scope">
									{{ datelineToDate(scope.row.created_at) }}
								</template>
							</el-table-column>
							<el-table-column fixed="right" label="操作" width="100" align="center">
								<template #default="scope">
									<el-icon
										@click="editClick(scope.row)"
										class="cursor-pointer"
										style="color: #464bd7; margin-right: 8px"
										:size="20"
										><edit
									/></el-icon>
									<el-popconfirm
										title="确定要删除吗?"
										@confirm="deleteHandler(scope.row, 1)"
									>
										<template #reference>
											<el-icon
												class="cursor-pointer"
												style="color: crimson; margin-right: 8px"
												:size="20"
												><delete
											/></el-icon>
										</template>
									</el-popconfirm>
								</template>
							</el-table-column>
						</el-table>
					</el-tab-pane>
					<el-tab-pane label="已结束活动" name="completed">
						<el-table
							border
							style="width: 100%"
							:data="tableData2"
							:max-height="tableHeight2"
							:row-style="{ height: '40px' }"
							:header-cell-style="{ background: '#ebeef5', color: '#333' }"
						>
							<el-table-column fixed prop="id" label="ID" width="70" align="center">
							</el-table-column>
							<el-table-column prop="subject" label="标题" :show-overflow-tooltip="true">
							</el-table-column>
							<el-table-column prop="event_geo.name" label="地点" width="90" align="center">
							</el-table-column>
							<el-table-column label="展示/开始结束时间" width="180" align="center">
								<template #default="scope">
									{{ datelineToDate(scope.row.push_begin_date) }}
									{{ datelineToDate(scope.row.push_end_date) }}
								</template>
							</el-table-column>
							<el-table-column label="活动/开始结束时间	" width="180" align="center">
								<template #default="scope">
									{{ datelineToDate(scope.row.event_begin_date) }}
									{{ datelineToDate(scope.row.event_end_date) }}
								</template>
							</el-table-column>
							<el-table-column label="创建时间" width="180" align="center">
								<template #default="scope">
									{{ datelineToDate(scope.row.created_at) }}
								</template>
							</el-table-column>
							<el-table-column fixed="right" label="操作" width="100" align="center">
								<template #default="scope">
									<el-icon
										@click="editClick(scope.row)"
										class="cursor-pointer"
										style="color: #464bd7; margin-right: 8px"
										:size="20"
										><edit
									/></el-icon>
								</template>
							</el-table-column>
						</el-table>
					</el-tab-pane>
				</el-tabs>
			</el-col>
		</el-row>
		<el-row class="page">
			<el-pagination
				v-if="activeTab == 'current'"
				class="pagination"
				background
				:currentPage="currentPage"
				:page-size="pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
			<el-pagination
				v-else
				class="pagination"
				background
				:currentPage="currentPage2"
				:page-size="pageSize2"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total2"
				@size-change="handleSizeChange2"
				@current-change="handleCurrentChange2"
			/>
		</el-row>

		<el-dialog v-model="editVisible" title="表单" width="800">
			<el-form
				:model="form"
				label-width="120px"
				style="max-width: 800px"
				ref="addRef"
				:rules="rules"
			>
				<el-form-item label="标题" required prop="subject">
					<el-input v-model="form.subject" />
				</el-form-item>

				<el-form-item label="内容" required prop="content">
					<el-input v-model="form.content" type="textarea" rows="6" />
				</el-form-item>

				<div style="padding-left: 125px">
					<strong style="width: 275px; display: inline-block">活动开始时间</strong>
					<strong style="width: 275px; display: inline-block">活动结束时间</strong>
				</div>
				<el-form-item label="活动时间">
					<el-date-picker
						v-model="form.event_begin_date"
						type="datetime"
						placeholder="请选择开始时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"										
					/>
					<span class="split">-</span>
					<el-date-picker
						v-model="form.event_end_date"
						type="datetime"
						placeholder="请选择结束时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"						
					/>
				</el-form-item>
				<div style="padding-left: 125px">
					<strong style="width: 275px; display: inline-block">推送开始时间</strong>
					<strong style="width: 275px; display: inline-block">推送结束时间</strong>
				</div>
				<el-form-item label="推送时间" required>
					<el-date-picker
						v-model="form.push_begin_date"
						type="datetime"
						placeholder="请选择开始时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
					/>
					<span class="split">-</span>
					<el-date-picker
						v-model="form.push_end_date"
						type="datetime"
						placeholder="请选择结束时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
					/>
				</el-form-item>
				<el-form-item label="专业" :required="true">
					<el-checkbox-group v-model="form.major">
						<el-checkbox
							v-for="obj in major"
							:key="obj.id"
							:label="obj.short"
							:value="obj.id"
						/>
					</el-checkbox-group>
				</el-form-item>
				<el-form-item label="地点" :required="true">
					<el-row :gutter="20">
						<el-col :span="8" class="location">
							<el-select
								filterable
								v-model="form.country_id"
								placeholder="请选择国家"								
								:empty-values="[0, '0', null, undefined]"
								@change="handleCountryChange"
								style="width: 120px"
							>
								<el-option
									v-for="country in countries"
									:key="country.id"
									:label="country.name"
									:value="country.id"
								/>
							</el-select>
						</el-col>
						<el-col :span="8" class="location">
							<el-select
								filterable
								v-model="form.province_id"
								placeholder="请选择地区"								
								:disabled="regions.length === 0"
								:empty-values="[0, '0', null, undefined]"
								@change="handleRegionChange"
								style="width: 120px"
							>
								<el-option
									v-for="region in regions"
									:key="region.id"
									:label="region.name"
									:value="region.id"
								/>
							</el-select>
						</el-col>
						<el-col :span="8" class="location">
							<el-select
								filterable
								v-model="form.city_id"
								placeholder="请选择城市"
								:disabled="cities.length === 0"
								:empty-values="[0, '0', null, undefined]"
								style="width: 120px"
							>
								<el-option
									v-for="city in cities"
									:key="city.id"
									:label="city.name"
									:value="city.id"
								/>
							</el-select>
						</el-col>
					</el-row>
				</el-form-item>

				<el-form-item>
					<div class="form-submit">
						<el-button type="primary" @click="submitForm(addRef)">提交</el-button>
						<el-button @click="editVisible = false">取消</el-button>
					</div>
				</el-form-item>
			</el-form>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from "element-plus";
import { Edit, Delete, Search, CirclePlus } from "@element-plus/icons-vue";
import { ref, reactive, onMounted, nextTick, onUnmounted, computed } from "vue";
import type { FormInstance } from "element-plus";
import { useCool } from "/@/cool";
import { removeEmptyFromObject } from "/@/cool/utils";
import { datelineToDate } from "/@/cool/utils";

const { service } = useCool();

const activeTab = ref("current");

const search = reactive({
	id: "",
	subject: ""
});

const countries = ref([]);
const regions = ref([]);
const cities = ref([]);

const tableHeight = ref(0);
const tableHeight2 = ref(0);
const tableData = ref([]);
const tableData2 = ref([]);
const currentPage = ref(1);
const currentPage2 = ref(1);
const pageSize = ref(50);
const pageSize2 = ref(50);
const total = ref(0);
const total2 = ref(0);

const visible = ref(false);
const editVisible = ref(false);
const addRef = ref<FormInstance>();

const major = ref([
  { id: 116, short: 'MBA', name: 'MBA' },
  { id: 220, short: 'MiM', name: 'MiM' }
]);

let form: any = reactive({
	id: 0,
	subject: "",
	content: "",	
	event_begin_date: "",
	event_end_date: "",
	push_begin_date: "",
	push_end_date: "",	
	location: "",
	major: [],
	country_id: "",
	province_id: "",
	city_id: "",
});

let rules = reactive({
	subject: [{ required: true, message: "请输入标题", trigger: "blur" }],
	content: [{ required: true, message: "请输入内容", trigger: "blur" }],	
	push_begin_date: [{ required: true, message: "请输入推送开始时间", trigger: "blur" }],	
	push_end_date: [{ required: true, message: "请输入推送结束时间", trigger: "blur" }],	
	location: [{ required: true, message: "请输入地点", trigger: "blur" }],	
});

const handleCountryChange = (country_id) => {
	form.province_id = "";
	form.city_id = "";
	regions.value = [];
	cities.value = [];

	loadGeo(country_id, "regions");
};

const handleRegionChange = (province_id) => {	
	form.city_id = "";
	cities.value = [];
	loadGeo(province_id, "cities");
};

const loadGeo = (parent_id = "", target = "") => {
	service.base.common.event
		.geo({ parent_id })
		.then((res) => {
			if (target === "countries") {
				countries.value = res;
			} else if (target === "regions") {
				regions.value = res;
			} else if (target === "cities") {
				cities.value = res;
			}
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const submitForm = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate((valid, fields) => {
		if (!valid) return;

		if(form.id == 0) {			
			service.base.common.event
				.ieseAdd({
					...form
				})
				.then((res) => {
					ElMessage({
						message: "已提交!",
						type: "success"
					});
					visible.value = false;
					editVisible.value = false;

					load();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});			
		} else {
			service.base.common.event
			.ieseUpdate({
				...form
			})
			.then((res) => {
				ElMessage({
					message: "已提交!",
					type: "success"
				});
				visible.value = false;
				editVisible.value = false;

				refresh();
			})
			.catch((err) => {
				ElMessage.error(err.message);
			});
		}
	});
};

const deleteHandler = (row: any, type) => {
	service.base.common.event
		.ieseDelete({
			id: row.id
		})
		.then((res) => {
			ElMessage({
				message: "已删除!",
				type: "success"
			});
			type === 1 ? refresh() : refresh2();		
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const add = () => {
	reset();

	editVisible.value = true;
};

const reset = () => {
	form.id = 0;
	form.subject = "";
	form.content = "";
	form.event_begin_date = "";
	form.event_end_date = "";
	form.push_begin_date = "";
	form.push_end_date = "";
	form.location = "";
	form.location_id = "";
	form.country_id = "";
	form.province_id = "";
	form.city_id = "";
	form.major = [];
};

const editClick = async (row: any) => {
	editVisible.value = true;

	form.id = row.id;
	form.subject = row.subject;
	form.content = row.content;	
	form.event_begin_date = row.event_begin_date;	
	form.event_end_date = row.event_end_date;	
	form.push_begin_date = row.push_begin_date;	
	form.push_end_date = row.push_end_date;	
	form.location = row.location;	
	form.location_id = row.location_id;	
	form.country_id = row.country_id;	
	form.province_id = row.province_id;	
	form.city_id = row.city_id;	
	form.major = row.major ? row.major.split(',').map((id: string) => Number(id)) : [];

	await loadGeo(form.country_id, "regions");
	await loadGeo(form.province_id, "cities");
};

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};
const handleSizeChange2 = (val: number) => {
	pageSize2.value = val;
	refresh2();
};
const handleCurrentChange2 = (val: number) => {
	currentPage2.value = val;
	refresh2();
};

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.event
		.ieseList({
			...s,
			page: currentPage.value,
			pageSize: pageSize.value
		})
		.then((res) => {
			tableData.value = res[0];
			total.value = res[1];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const refresh2 = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.event
		.ieseList({
			...s,
			status: -1,
			page: currentPage2.value,
			pageSize: pageSize2.value
		})
		.then((res) => {
			tableData2.value = res[0];
			total2.value = res[1];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const load = () => {
	refresh();
	refresh2();
};

load();

onMounted(() => {
	loadGeo("", "countries");

	nextTick(() => {
		tableHeight.value = window.innerHeight - 230;
		tableHeight2.value = window.innerHeight - 230;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 230;
			tableHeight2.value = window.innerHeight - 230;
		};
	});
});

onUnmounted(() => {
	reset();
});
</script>

<style lang="scss" scoped>
@import "../css/index.scss";
</style>
