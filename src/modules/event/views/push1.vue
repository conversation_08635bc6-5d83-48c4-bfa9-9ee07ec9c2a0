<template>
	<div class="wrapper">
		<el-row class="opt">
			<el-col :span="4">
				<el-button type="primary" @click="add">添加</el-button>
			</el-col>
			<el-col :span="20" style="text-align: right">
				<div class="weekHost">
					<span class="item">
						本周热门：
						<el-select v-model="weekHot" style="width: 60px" @change="wwwHotChange">
							<el-option
								v-for="item in push2WeekHot"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</span>
					<span class="item"
						>广告显示位置：
						<el-radio-group v-model="adPos" @change="push1WwwPositionChange">
							<el-radio value="top">上</el-radio>
							<el-radio value="bottom">下</el-radio>
						</el-radio-group></span
					>
					<span class="item">
						{{ webShowTxt }}
						<el-input v-model="webShowVal" style="width: 60px" placeholder="条数" />
						<el-button type="primary" @click="wsClick" link> 更改 </el-button></span
					>
				</div>
			</el-col>
		</el-row>
		<el-row class="list">
			<el-col :span="24">
				<el-tabs v-model="activeTab">
					<el-tab-pane label="当前活动" name="current">
						<el-table
							border
							style="width: 100%"
							:data="tableData"
							:max-height="tableHeight"
							:row-style="{ height: '40px' }"
							:header-cell-style="{ background: '#ebeef5', color: '#333' }"
						>
							<el-table-column label="地点" width="90" align="center">
								<template #default="scope">
									<el-button type="primary" text>
										{{
											scope.row?.event_geo?.name.replace(
												"申请截止",
												"Deadline"
											)
										}}
									</el-button>
									{{ datelineToDate(scope.row.event_begin_date, "MM-DD") }}
								</template>
							</el-table-column>
							<el-table-column
								prop="subject"
								label="标题"
								:show-overflow-tooltip="true"
								align="left"
							>
							</el-table-column>
							<el-table-column label="展示/开始结束时间" width="180" align="center">
								<template #default="scope">
									{{ datelineToDate(scope.row.push_begin_date) }}
									{{ datelineToDate(scope.row.push_end_date) }}
								</template>
							</el-table-column>
							<el-table-column label="活动/开始结束时间	" width="180" align="center">
								<template #default="scope">
									{{ datelineToDate(scope.row.event_begin_date) }}
									{{ datelineToDate(scope.row.event_end_date) }}
								</template>
							</el-table-column>
							<el-table-column label="最新标签时间" width="180" align="center">
								<template #default="scope">
									{{ datelineToDate(scope.row.new_flag_date) }}
								</template>
							</el-table-column>

							<el-table-column fixed="right" label="操作" width="110" align="center">
								<template #default="scope">
									<el-icon
										@click="editClick(scope.row)"
										class="cursor-pointer"
										style="color: #464bd7; margin-right: 8px"
										:size="20"
										><Edit
									/></el-icon>

									<el-popconfirm
										title="确定要下线吗?"
										@confirm="offlineHandler(scope.row)"
									>
										<template #reference>
											<el-icon
												class="cursor-pointer"
												style="color: lightseagreen; margin-right: 8px"
												:size="20"
												><bottom
											/></el-icon>
										</template>
									</el-popconfirm>

									<el-popconfirm
										title="确定要删除吗?"
										@confirm="deleteHandler(scope.row, 1)"
									>
										<template #reference>
											<el-icon
												class="cursor-pointer"
												style="color: crimson; margin-right: 8px"
												:size="20"
												><delete
											/></el-icon>
										</template>
									</el-popconfirm>
								</template>
							</el-table-column>
						</el-table>
					</el-tab-pane>
					<el-tab-pane label="已结束活动" name="completed">
						<el-table
							border
							style="width: 100%"
							:data="tableData2"
							:max-height="tableHeight2"
							:row-style="{ height: '40px' }"
							:header-cell-style="{ background: '#ebeef5', color: '#333' }"
						>
							<el-table-column label="地点" width="90" align="center">
								<template #default="scope">
									<el-button type="primary" text>
										{{
											scope.row?.event_geo?.name.replace(
												"申请截止",
												"Deadline"
											)
										}}
									</el-button>
									{{ datelineToDate(scope.row.event_begin_date, "MM-DD") }}
								</template>
							</el-table-column>
							<el-table-column
								prop="subject"
								label="标题"
								:show-overflow-tooltip="true"
								align="left"
							>
							</el-table-column>
							<el-table-column label="展示/开始结束时间" width="180" align="center">
								<template #default="scope">
									{{ datelineToDate(scope.row.push_begin_date) }}
									{{ datelineToDate(scope.row.push_end_date) }}
								</template>
							</el-table-column>
							<el-table-column label="活动/开始结束时间	" width="180" align="center">
								<template #default="scope">
									{{ datelineToDate(scope.row.event_begin_date) }}
									{{ datelineToDate(scope.row.event_end_date) }}
								</template>
							</el-table-column>
							<el-table-column label="最新标签时间" width="180" align="center">
								<template #default="scope">
									{{ datelineToDate(scope.row.new_flag_date) }}
								</template>
							</el-table-column>

							<el-table-column fixed="right" label="操作" width="100" align="center">
								<template #default="scope">
									<el-icon
										@click="editClick(scope.row)"
										class="cursor-pointer"
										style="color: #464bd7; margin-right: 8px"
										:size="20"
										><Edit
									/></el-icon>

									<el-popconfirm
										title="确定要删除吗?"
										@confirm="deleteHandler(scope.row, 1)"
									>
										<template #reference>
											<el-icon
												class="cursor-pointer"
												style="color: crimson; margin-right: 8px"
												:size="20"
												><delete
											/></el-icon>
										</template>
									</el-popconfirm>
								</template>
							</el-table-column>
						</el-table>
					</el-tab-pane>
				</el-tabs>
			</el-col>
		</el-row>
		<el-row class="page">
			<el-pagination
				v-if="activeTab == 'current'"
				class="pagination"
				background
				:currentPage="currentPage"
				:page-size="pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
			<el-pagination
				v-else
				class="pagination"
				background
				:currentPage="currentPage2"
				:page-size="pageSize2"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total2"
				@size-change="handleSizeChange2"
				@current-change="handleCurrentChange2"
			/>
		</el-row>

		<el-dialog v-model="push1Visible" title="推1" width="1000">
			<el-form
				:model="push1Form"
				label-width="120px"
				style="max-width: 1000px"
				ref="push1Ref"
				:rules="push1Rules"
			>
				<el-form-item label="通栏" :required="true" size="small">
					<el-radio-group v-model="push1Form.html">
						<el-radio value="1">是</el-radio>
						<el-radio value="0">否</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item
					v-if="push1Form.html == 0"
					label="WWW URL"
					:required="true"
					prop="url1"
				>
					<el-input v-model="push1Form.url1" placeholder="请入WWW URL" />
				</el-form-item>
				<el-form-item
					v-if="push1Form.html == 0"
					label="论坛 URL"
					:required="true"
					prop="url2"
				>
					<el-input v-model="push1Form.url2" placeholder="请入论坛 URL" />
				</el-form-item>
				<div class="locationWrap">
					<el-form-item label="活动地区" v-if="push1Form.html == 0">
						<el-row>
							<el-checkbox :checked="true" disabled>{{
								push1Form?.geo?.name
							}}</el-checkbox>
						</el-row>
					</el-form-item>
					<el-form-item
						v-if="push1Form.html == 0"
						label="活动标题"
						:required="true"
						prop="subject"
					>
						<el-input v-model="push1Form.subject" placeholder="请输入活动标题" />
					</el-form-item>
					<el-form-item v-else label="活动内容" :required="true">
						<el-input
							v-model="push1Form.subject"
							placeholder="请输入活动内容"
							type="textarea"
							rows="5"
						/>
					</el-form-item>
					<div style="padding-left: 125px">
						<strong style="width: 275px; display: inline-block">活动开始时间</strong>
						<strong style="width: 275px; display: inline-block">活动结束时间</strong>
					</div>
					<el-form-item label="活动时间">
						<el-date-picker
							v-model="push1Form.event_begin_date"
							type="datetime"
							placeholder="请选择开始时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
							disabled
						/>
						<span class="split">-</span>
						<el-date-picker
							v-model="push1Form.event_end_date"
							type="datetime"
							placeholder="请选择结束时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
							disabled
						/>
					</el-form-item>
					<div style="padding-left: 125px">
						<strong style="width: 275px; display: inline-block">推送开始时间</strong>
						<strong style="width: 275px; display: inline-block">推送结束时间</strong>
					</div>
					<el-form-item label="推送时间">
						<el-date-picker
							v-model="push1Form.push_begin_date"
							type="datetime"
							placeholder="请选择开始时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
						/>
						<span class="split">-</span>
						<el-date-picker
							v-model="push1Form.push_end_date"
							type="datetime"
							placeholder="请选择结束时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
						/>
					</el-form-item>
					<el-form-item label="最新标签下线时间" :required="true" prop="new_flag_date">
						<el-date-picker
							v-model="push1Form.new_flag_date"
							type="datetime"
							placeholder="请选择开始时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
						/>
					</el-form-item>
				</div>

				<el-form-item>
					<div class="form-submit">
						<el-button type="primary" @click="submitPush1Form(push1Ref)"
							>提交</el-button
						>
						<el-button @click="push1Visible = false">取消</el-button>
					</div>
				</el-form-item>

				<el-form-item v-if="push1Form.html == 1" label="HTML标签示例">
					<el-text style="background: #effaf5">{{ tip }} </el-text>
				</el-form-item>
			</el-form>
		</el-dialog>

		<el-dialog v-model="push1AddVisible" title="添加 推1" width="1000">
			<el-form
				:model="push1AddForm"
				label-width="120px"
				style="max-width: 1000px"
				ref="push1AddRef"
				:rules="push1AddRules"
			>
				<div class="locationWrap">
					<el-form-item label="活动内容" :required="true" prop="subject">
						<el-input
							v-model="push1AddForm.subject"
							placeholder="请输入活动内容"
							type="textarea"
							rows="5"
						/>
					</el-form-item>
					<div style="padding-left: 125px">
						<strong style="width: 275px; display: inline-block">活动开始时间</strong>
						<strong style="width: 275px; display: inline-block">活动结束时间</strong>
					</div>
					<el-form-item label="活动时间">
						<el-date-picker
							v-model="push1AddForm.event_begin_date"
							type="datetime"
							placeholder="请选择开始时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
						/>
						<span class="split">-</span>
						<el-date-picker
							v-model="push1AddForm.event_end_date"
							type="datetime"
							placeholder="请选择结束时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
						/>
					</el-form-item>
					<div style="padding-left: 125px">
						<strong style="width: 275px; display: inline-block">推送开始时间</strong>
						<strong style="width: 275px; display: inline-block">推送结束时间</strong>
					</div>
					<el-form-item label="推送时间">
						<el-date-picker
							v-model="push1AddForm.push_begin_date"
							type="datetime"
							placeholder="请选择开始时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
						/>
						<span class="split">-</span>
						<el-date-picker
							v-model="push1AddForm.push_end_date"
							type="datetime"
							placeholder="请选择结束时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
						/>
					</el-form-item>
					<el-form-item label="最新标签下线时间" :required="true" prop="new_flag_date">
						<el-date-picker
							v-model="push1AddForm.new_flag_date"
							type="datetime"
							placeholder="请选择开始时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
						/>
					</el-form-item>
				</div>

				<el-form-item>
					<div class="form-submit">
						<el-button type="primary" @click="submitPush1AddForm(push1AddRef)"
							>提交</el-button
						>
						<el-button @click="push1Visible = false">取消</el-button>
					</div>
				</el-form-item>

				<el-form-item label="HTML标签示例">
					<el-text style="background: #effaf5">{{ tip }} </el-text>
				</el-form-item>
			</el-form>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import { Edit, Delete, Bottom } from "@element-plus/icons-vue";
import { ref, reactive, onMounted, onUnmounted, nextTick } from "vue";
import type { FormInstance } from "element-plus";
import { useCool } from "/@/cool";
import { removeEmptyFromObject } from "/@/cool/utils";
import { datelineToDate } from "/@/cool/utils";
import { push2WeekHot } from "../data/index";

const { service } = useCool();

const activeTab = ref("current");
const weekHot = ref("");
const adPos = ref("");
const webShowTxt = ref("");
const webShowVal = ref("");

const search = reactive({
	id: ""
});

const tableHeight = ref(0);
const tableHeight2 = ref(0);
const tableData = ref([]);
const tableData2 = ref([]);
const currentPage = ref(1);
const currentPage2 = ref(1);
const pageSize = ref(50);
const pageSize2 = ref(50);
const total = ref(0);
const total2 = ref(0);
const tip =
	'<tr><td colspan="2" style="padding: 5px 0;"><i></i><a href="https://www.chasedream.com/show.aspx?id=31217&amp;cid=11|||https://forum.chasedream.com/thread-1374459-1-1.html" target="_blank">UCR商学院ABLE未来管理领袖体验营线上项目<img src="图片地址（图片请放到a标签内）"></a></td></tr>';

const push1Visible = ref(false);
const push1Ref = ref<FormInstance>();
const push1Form = reactive({
	id: 0,
	html: "",
	url1: "",
	url2: "",
	subject: "",
	event_begin_date: "",
	event_end_date: "",
	push_begin_date: "",
	push_end_date: "",
	new_flag_date: ""
});
const push1Rules = reactive({
	url1: [{ required: true, message: "请输入WWW URL", trigger: "blur" }],
	url2: [{ required: true, message: "请输入论坛 URL", trigger: "blur" }],
	subject: [{ required: true, message: "请输入标题", trigger: "blur" }],
	new_flag_date: [{ required: true, message: "最新标签下线时间", trigger: "blur" }]
});

const submitPush1Form = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;

	await formEl.validate((isValid, fields) => {
		if (!isValid) return;

		const obj = Object.assign({}, push1Form);
		delete obj.geo;

		service.base.common.event
			.releaseEditOne({
				...obj
			})
			.then((res) => {
				ElMessage({
					message: "修改成功!",
					type: "success"
				});

				push1Visible.value = false;
				load();
			})
			.catch((err) => {
				ElMessage.error(err.message);
			});
	});
};

const push1AddVisible = ref(false);
const push1AddRef = ref<FormInstance>();
const push1AddForm = reactive({
	html: 1,
	type: 1,
	subject: "",
	event_begin_date: "",
	event_end_date: "",
	push_begin_date: "",
	push_end_date: "",
	new_flag_date: ""
});
const push1AddRules = reactive({
	subject: [{ required: true, message: "请输入标题", trigger: "blur" }],
	new_flag_date: [{ required: true, message: "最新标签下线时间", trigger: "blur" }]
});

const submitPush1AddForm = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;

	await formEl.validate((isValid, fields) => {
		if (!isValid) return;

		const obj = Object.assign({}, push1AddForm);

		service.base.common.event
			.push1Add({
				...obj
			})
			.then((res) => {
				ElMessage({
					message: "修改成功!",
					type: "success"
				});

				push1AddVisible.value = false;
				load();
			})
			.catch((err) => {
				ElMessage.error(err.message);
			});
	});
};

const add = () => {
	reset();

	push1AddVisible.value = true;
};

const editClick = (row: any) => {
	push1Visible.value = true;

	getRelease(row);
};

const push1Count = () => {
	service.base.common.event
		.push1Count()
		.then((res) => {
			webShowTxt.value = `web显示条数 （${res.value}）`;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const wsClick = () => {
	service.base.common.event
		.push1CountUpdate({ value: webShowVal.value })
		.then((res) => {
			webShowTxt.value = `web显示条数 （${webShowVal.value}）`;

			ElMessage({
				message: "修改成功!",
				type: "success"
			});
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const wwwHotChange = (value) => {
	service.base.common.event
		.wwwHotUpdate({ value })
		.then((res) => {
			ElMessage({
				message: "修改成功!",
				type: "success"
			});
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const wwwHot = () => {
	service.base.common.event
		.wwwHot({})
		.then((res) => {
			weekHot.value = res.value;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const push1WwwPositionChange = (value) => {
	service.base.common.event
		.push1WwwPositionUpdate({ value })
		.then((res) => {
			ElMessage({
				message: "修改成功!",
				type: "success"
			});
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const push1WwwPosition = () => {
	service.base.common.event
		.push1WwwPosition({})
		.then((res) => {
			adPos.value = res.value;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const offlineHandler = (row: any) => {
	service.base.common.event
		.offline({
			id: row.id,
			type: 2
		})
		.then((res) => {
			ElMessage({
				message: "已下线!",
				type: "success"
			});
			refresh();
			refresh2();
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const deleteHandler = (row: any, type) => {
	service.base.common.event
		.releaseDelete({
			id: row.id
		})
		.then((res) => {
			ElMessage({
				message: "已删除!",
				type: "success"
			});
			type === 1 ? refresh() : refresh2();
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};
const handleSizeChange2 = (val: number) => {
	pageSize2.value = val;
	refresh2();
};
const handleCurrentChange2 = (val: number) => {
	currentPage2.value = val;
	refresh2();
};

const getRelease = (row) => {
	service.base.common.event
		.getReleaseOne({ id: row.id })
		.then((res) => {
			push1Form.id = res.id;
			push1Form.html = res.html.toString();
			push1Form.url1 = res.url1;
			push1Form.url2 = res.url2;
			push1Form.subject = res.subject;
			push1Form.event_begin_date = res.event_begin_date;
			push1Form.event_end_date = res.event_end_date;
			push1Form.push_begin_date = res.push_begin_date;
			push1Form.push_end_date = res.push_end_date;
			push1Form.new_flag_date = res.new_flag_date;
			push1Form.geo = res.event_geo;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

function reset() {
	push1Form.id = 0;
	push1Form.html = "";
	push1Form.url1 = "";
	push1Form.url2 = "";
	push1Form.subject = "";
	push1Form.event_begin_date = "";
	push1Form.event_end_date = "";
	push1Form.push_begin_date = "";
	push1Form.push_end_date = "";
	push1Form.new_flag_date = "";

	push1AddForm.html = 1;
	push1AddForm.subject = "";
	push1AddForm.event_begin_date = "";
	push1AddForm.event_end_date = "";
	push1AddForm.push_begin_date = "";
	push1AddForm.push_end_date = "";
	push1AddForm.new_flag_date = "";
}

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.event
		.releaseList({
			...s,
			type: 1,
			page: currentPage.value,
			pageSize: pageSize.value
		})
		.then((res) => {
			tableData.value = res[0];
			total.value = res[1];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};
const refresh2 = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.event
		.releaseList({
			...s,
			status: -1,
			type: 1,
			page: currentPage2.value,
			pageSize: pageSize2.value
		})
		.then((res) => {
			tableData2.value = res[0];
			total2.value = res[1];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const load = () => {
	refresh();
	refresh2();
};

load();
wwwHot();
push1WwwPosition();
push1Count();

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 290;
		tableHeight2.value = window.innerHeight - 290;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 290;
			tableHeight2.value = window.innerHeight - 290;
		};
	});
});

onUnmounted(() => {
	reset();
});
</script>

<style lang="scss" scoped>
@import "../css/push.scss";
</style>
