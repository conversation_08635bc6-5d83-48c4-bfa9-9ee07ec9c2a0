<template>
	<div class="wrapper">
		<el-row class="list">
			<el-col :span="24">
				<el-table
					border
					style="width: 100%"
					:data="tableData"
					:max-height="tableHeight"
					:row-style="{ height: '40px' }"
					:header-cell-style="{ background: '#ebeef5', color: '#333' }"
				>
					<el-table-column fixed prop="id" label="ID" width="70" align="center">
					</el-table-column>
					<el-table-column prop="subject" label="标题" :show-overflow-tooltip="true">
					</el-table-column>
					<el-table-column width="150" prop="author" label="作者" align="center">
					</el-table-column>
					<el-table-column label="创建时间" width="180" align="center">
						<template #default="scope">
							{{ datelineToDate(scope.row.created_at) }}
						</template>
					</el-table-column>
					<el-table-column fixed="right" label="操作" width="100" align="center">
						<template #default="scope">
							<el-icon
								@click="editClick(scope.row)"
								class="cursor-pointer"
								style="color: #464bd7; margin-right: 8px"
								:size="20"
								><edit
							/></el-icon>
						</template>
					</el-table-column>
				</el-table>
			</el-col>
		</el-row>
		<el-row class="page">
			<el-pagination
				class="pagination"
				background
				:currentPage="currentPage"
				:page-size="pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</el-row>

		<el-dialog v-model="editVisible" title="编辑" width="800">
			<el-form
				:model="form"
				label-width="120px"
				style="max-width: 800px"
				ref="addRef"
				:rules="rules"
			>
				<el-form-item label="标题" required prop="subject">
					<el-input v-model="form.subject" />
				</el-form-item>

				<el-form-item label="内容" required prop="content">
					<el-input v-model="form.content" type="textarea" rows="6" />
				</el-form-item>

				<el-form-item label="作者" required prop="author">
					<el-input v-model="form.author" />
				</el-form-item>

				<el-form-item>
					<div class="form-submit">
						<el-button type="primary" @click="submitForm(addRef)">提交</el-button>
						<el-button @click="editVisible = false">取消</el-button>
					</div>
				</el-form-item>
			</el-form>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from "element-plus";
import { Edit, Delete, Search, CirclePlus } from "@element-plus/icons-vue";
import { ref, reactive, onMounted, nextTick, onUnmounted, computed } from "vue";
import type { FormInstance } from "element-plus";
import { useCool } from "/@/cool";
import { removeEmptyFromObject } from "/@/cool/utils";
import { datelineToDate } from "/@/cool/utils";

const { service } = useCool();

const search = reactive({
	id: "",
	subject: ""
});

const tableHeight = ref(0);
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(50);
const total = ref(0);

const visible = ref(false);
const editVisible = ref(false);
const addRef = ref<FormInstance>();

let form: any = reactive({
	id: 0,
	subject: "",
	content: "",
	author: ""
});

let rules = reactive({
	subject: [{ required: true, message: "请输入标题", trigger: "blur" }],
	content: [{ required: true, message: "请输入内容", trigger: "blur" }],
	author: [{ required: true, message: "请输入作者", trigger: "blur" }]
});

const submitForm = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate((valid, fields) => {
		if (!valid) return;

		service.base.common.event
			.acUpdate({
				...form
			})
			.then((res) => {
				ElMessage({
					message: "已提交!",
					type: "success"
				});
				visible.value = false;
				editVisible.value = false;

				refresh();
			})
			.catch((err) => {
				ElMessage.error(err.message);
			});
	});
};

const reset = () => {
	form.id = 0;
	form.subject = "";
	form.content = "";
	form.author = "";
};

const editClick = (row: any) => {
	editVisible.value = true;

	form.id = row.id;
	form.subject = row.subject;
	form.content = row.content;
	form.author = row.author;
};

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.event
		.acList({
			...s,
			page: currentPage.value,
			pageSize: pageSize.value
		})
		.then((res) => {
			tableData.value = res[0];
			total.value = res[1];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

refresh();

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 230;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 230;
		};
	});
});

onUnmounted(() => {
	reset();
});
</script>

<style lang="scss" scoped>
@import "../css/index.scss";
</style>
