<template>
	<div class="wrapper">
		<el-row class="opt">
			<el-col :span="12">
				<el-button @click="visible = true" type="primary">添加</el-button>
			</el-col>
		</el-row>
		<el-row class="list">
			<el-col :span="24">
				<el-table
					border
					style="width: 100%"
					:data="tableData"
					:max-height="tableHeight"
					:row-style="{ height: '40px' }"
					:header-cell-style="{ background: '#ebeef5', color: '#333' }"
				>
					<el-table-column fixed prop="id" label="ID" width="70" align="center">
					</el-table-column>
					<el-table-column
						prop="nickname"
						label="账号"
						:show-overflow-tooltip="true"
						align="center"
					>
					</el-table-column>
					<el-table-column width="150" label="类型" align="center">
						<template #default="scope">
							{{ scope.row.type == 1 ? "论坛用户" : "WWW用户" }}
						</template>
					</el-table-column>
					<el-table-column fixed="right" label="操作" width="100" align="center">
						<template #default="scope">
							<el-icon
								@click="editClick(scope.row)"
								class="cursor-pointer"
								style="color: #464bd7; margin-right: 8px"
								:size="20"
								><edit
							/></el-icon>

							<el-popconfirm
								title="确定要删除吗?"
								@confirm="deleteHandler(scope.row, 1)"
							>
								<template #reference>
									<el-icon
										class="cursor-pointer"
										style="color: crimson; margin-right: 8px"
										:size="20"
										><delete
									/></el-icon>
								</template>
							</el-popconfirm>
						</template>
					</el-table-column>
				</el-table>
			</el-col>
		</el-row>
		<el-row class="page">
			<el-pagination
				class="pagination"
				background
				:currentPage="currentPage"
				:page-size="pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</el-row>

		<el-dialog v-model="visible" title="添加" width="800">
			<el-form
				:model="form"
				label-width="120px"
				style="max-width: 800px"
				ref="addRef"
				:rules="rules"
			>
				<el-form-item label="昵称" required prop="nickname">
					<el-input v-model="form.nickname" placeholder="请输入昵称" />
				</el-form-item>

				<el-form-item label="账号" required prop="username">
					<el-input v-model="form.username" placeholder="请输入账号" />
				</el-form-item>

				<el-form-item label="密码" required prop="password">
					<el-input v-model="form.password" type="password" placeholder="请输入密码" />
				</el-form-item>

				<el-form-item label="用户类型" prop="type" required>
					<el-radio-group v-model="form.type">
						<el-radio value="1">论坛用户</el-radio>
						<el-radio value="2">WWW用户</el-radio>
					</el-radio-group>
				</el-form-item>

				<el-form-item>
					<div class="form-submit">
						<el-button type="primary" @click="submitForm(addRef)">提交</el-button>
						<el-button @click="visible = false">取消</el-button>
					</div>
				</el-form-item>
			</el-form>
		</el-dialog>

		<el-dialog v-model="editVisible" title="编辑" width="800">
			<el-form
				:model="form"
				label-width="120px"
				style="max-width: 800px"
				ref="addRef"
				:rules="rules"
			>
				<el-form-item label="昵称" required prop="nickname">
					<el-input v-model="form.nickname" placeholder="请输入昵称" />
				</el-form-item>

				<el-form-item label="账号" required prop="username">
					<el-input v-model="form.username" placeholder="请输入账号" />
				</el-form-item>

				<el-form-item label="密码" prop="password">
					<el-input v-model="form.password" type="password" placeholder="请输入密码" />
				</el-form-item>

				<el-form-item label="用户类型" prop="type" required>
					<el-radio-group v-model="form.type">
						<el-radio value="1">论坛用户</el-radio>
						<el-radio value="2">WWW用户</el-radio>
					</el-radio-group>
				</el-form-item>

				<el-form-item>
					<div class="form-submit">
						<el-button type="primary" @click="submitForm(addRef)">提交</el-button>
						<el-button @click="editVisible = false">取消</el-button>
					</div>
				</el-form-item>
			</el-form>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from "element-plus";
import { Edit, Delete, Search, CirclePlus } from "@element-plus/icons-vue";
import { ref, reactive, onMounted, nextTick, onUnmounted, computed } from "vue";
import type { FormInstance } from "element-plus";
import { useCool } from "/@/cool";
import { removeEmptyFromObject } from "/@/cool/utils";

const { service } = useCool();

const search = reactive({
	id: "",
	subject: ""
});

const tableHeight = ref(0);
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(50);
const total = ref(0);

const visible = ref(false);
const editVisible = ref(false);
const addRef = ref<FormInstance>();

let form: any = reactive({
	id: 0,
	nickname: "",
	username: "",
	password: "",
	type: "1"
});

let rules = reactive({
	nickname: [{ required: true, message: "请输入昵称", trigger: "blur" }],
	username: [{ required: true, message: "请输入账号", trigger: "blur" }],
	password: [{ required: true, message: "请输入密码", trigger: "blur" }]
});

const submitForm = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate((valid, fields) => {
		if (!valid) return;

		if (form.password.length == 0) delete form.password;

		service.base.common.event
			.userCreateAndUpdate({
				...form
			})
			.then((res) => {
				ElMessage({
					message: "已提交!",
					type: "success"
				});
				visible.value = false;
				editVisible.value = false;

				refresh();
			})
			.catch((err) => {
				ElMessage.error(err.message);
			});
	});
};

const reset = () => {
	search.id = "";
	search.subject = "";

	form.id = 0;
	form.nickname = "";
	form.username = "";
	form.password = "";
};

const editClick = (row: any) => {
	editVisible.value = true;
	rules.password[0].required = false;

	form.id = row.id;
	form.nickname = row.nickname;
	form.username = row.username;
	form.type = row.type.toString();
};

const deleteHandler = (row: any, type) => {
	service.base.common.event
		.userDelete({
			id: row.id
		})
		.then((res) => {
			ElMessage({
				message: "已删除!",
				type: "success"
			});
			refresh();
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.event
		.userList({
			...s,
			page: currentPage.value,
			pageSize: pageSize.value
		})
		.then((res) => {
			tableData.value = res[0];
			total.value = res[1];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

refresh();

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 230;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 230;
		};
	});
});

onUnmounted(() => {
	reset();
});
</script>

<style lang="scss" scoped>
@import "../css/index.scss";
</style>
