<template>
	<div class="wrapper">
		<el-row class="opt">
			<el-col :span="4">
				<el-button type="primary" @click="add">添加</el-button>
			</el-col>
		</el-row>
		<el-row class="list">
			<el-col :span="24">
				<el-tabs v-model="activeTab">
					<el-tab-pane label="当前活动" name="current">
						<vue-draggable
							target="tbody"
							v-model="tableData"
							:animation="150"
							@end="onEnd"
						>
							<el-table
								border
								style="width: 100%"
								:data="tableData"
								:max-height="tableHeight"
								:row-style="{ height: '40px' }"
								:header-cell-style="{ background: '#ebeef5', color: '#333' }"
							>
								<el-table-column type="index" label="ID" width="90" align="center">
								</el-table-column>
								<el-table-column label="标题" align="left">
									<template #default="scope">
										<a
											:style="{
												color: scope.row.color
											}"
											:href="scope.row.url1"
											target="_blank"
											>{{ scope.row.subject }}</a
										>
									</template>
								</el-table-column>
								<el-table-column label="颜色" width="100" align="center">
									<template #default="scope">
										<el-dropdown placement="top" trigger="click">
											<el-button
												:color="scope.row.color"
												size="small"
											></el-button>
											<template #dropdown>
												<el-dropdown-menu>
													<el-dropdown-item>
														<div class="push3Color">
															<el-button
																v-for="(color, index) in push3Color"
																:class="
																	scope.row.color == color
																		? 'color'
																		: color
																"
																:key="index"
																:color="color"
																size="small"
																@click="
																	changeColor(scope.row, color)
																"
															></el-button>
														</div>
													</el-dropdown-item>
												</el-dropdown-menu>
											</template>
										</el-dropdown>
									</template>
								</el-table-column>
								<el-table-column label="结束时间" width="180" align="center">
									<template #default="scope">
										{{ datelineToDate(scope.row.event_end_date) }}
									</template>
								</el-table-column>

								<el-table-column
									fixed="right"
									label="操作"
									width="110"
									align="center"
								>
									<template #default="scope">
										<el-icon
											@click="editClick(scope.row)"
											class="cursor-pointer"
											style="color: #464bd7; margin-right: 8px"
											:size="20"
											><Edit
										/></el-icon>

										<el-popconfirm
											title="确定要下线吗?"
											@confirm="offlineHandler(scope.row)"
										>
											<template #reference>
												<el-icon
													class="cursor-pointer"
													style="color: lightseagreen; margin-right: 8px"
													:size="20"
													><bottom
												/></el-icon>
											</template>
										</el-popconfirm>

										<el-popconfirm
											title="确定要删除吗?"
											@confirm="deleteHandler(scope.row, 1)"
										>
											<template #reference>
												<el-icon
													class="cursor-pointer"
													style="color: crimson; margin-right: 8px"
													:size="20"
													><delete
												/></el-icon>
											</template>
										</el-popconfirm>
									</template>
								</el-table-column>
							</el-table>
						</vue-draggable>
					</el-tab-pane>
					<el-tab-pane label="已结束活动" name="completed">
						<el-table
							border
							style="width: 100%"
							:data="tableData2"
							:max-height="tableHeight2"
							:row-style="{ height: '40px' }"
							:header-cell-style="{ background: '#ebeef5', color: '#333' }"
						>
							<el-table-column
								prop="id"
								label="ID"
								width="90"
								align="center"
							></el-table-column>
							<el-table-column label="标题" align="left">
								<template #default="scope">
									<a
										:style="{
											color: scope.row.color
										}"
										:href="scope.row.url1"
										target="_blank"
										>{{ scope.row.subject }}</a
									>
								</template>
							</el-table-column>
							<el-table-column label="颜色" width="100" align="center">
								<template #default="scope">
									<el-dropdown placement="top" trigger="click">
										<el-button
											:color="scope.row.color"
											size="small"
										></el-button>
										<template #dropdown>
											<el-dropdown-menu>
												<el-dropdown-item>
													<div class="push3Color">
														<el-button
															v-for="(color, index) in push3Color"
															:class="
																scope.row.color == color
																	? 'color'
																	: color
															"
															:key="index"
															:color="color"
															size="small"
															@click="changeColor(scope.row, color)"
														></el-button>
													</div>
												</el-dropdown-item>
											</el-dropdown-menu>
										</template>
									</el-dropdown>
								</template>
							</el-table-column>
							<el-table-column label="结束时间" width="180" align="center">
								<template #default="scope">
									{{ datelineToDate(scope.row.event_end_date) }}
								</template>
							</el-table-column>

							<el-table-column fixed="right" label="操作" width="100" align="center">
								<template #default="scope">
									<el-icon
										@click="editClick(scope.row)"
										class="cursor-pointer"
										style="color: #464bd7; margin-right: 8px"
										:size="20"
										><Edit
									/></el-icon>
								</template>
							</el-table-column>
						</el-table>
					</el-tab-pane>
				</el-tabs>
			</el-col>
		</el-row>
		<el-row class="page">
			<el-pagination
				v-if="activeTab == 'current'"
				class="pagination"
				background
				:currentPage="currentPage"
				:page-size="pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
			<el-pagination
				v-else
				class="pagination"
				background
				:currentPage="currentPage2"
				:page-size="pageSize2"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total2"
				@size-change="handleSizeChange2"
				@current-change="handleCurrentChange2"
			/>
		</el-row>

		<el-dialog v-model="push3AddVisible" title="添加 推3" width="800">
			<el-form
				:model="push3AddForm"
				label-width="120px"
				style="max-width: 800px"
				ref="push3AddRef"
				:rules="push3AddRules"
			>
				<el-form-item label="标题" :required="true" prop="subject">
					<el-input v-model="push3AddForm.subject" placeholder="请输入标题" />
				</el-form-item>
				<el-form-item label="推送时间" required>
					<el-form-item prop="push_begin_date">
						<el-date-picker
							v-model="push3AddForm.push_begin_date"
							type="datetime"
							placeholder="请选择开始时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
						/>
					</el-form-item>
					<span class="split">-</span>
					<el-form-item prop="push_end_date">
						<el-date-picker
							v-model="push3AddForm.push_end_date"
							type="datetime"
							placeholder="请选择结束时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
						/>
					</el-form-item>
				</el-form-item>
				<el-form-item label="WWW URL" :required="true" prop="url1">
					<el-input v-model="push3AddForm.url1" placeholder="请入WWW URL" />
				</el-form-item>
				<el-form-item label="颜色" :required="true" prop="color">
					<el-button
						v-for="(color, index) in push3Color"
						:class="push3AddForm.color == color ? 'color' : color"
						:key="index"
						:color="color"
						size="small"
						@click="push3AddForm.color = color"
					></el-button>
				</el-form-item>

				<el-form-item>
					<div class="form-submit">
						<el-button type="primary" @click="submitPush3AddForm(push3AddRef)"
							>提交</el-button
						>
						<el-button @click="push3AddVisible = false">取消</el-button>
					</div>
				</el-form-item>
			</el-form>
		</el-dialog>

		<el-dialog v-model="push3Visible" title="编辑 推3" width="800">
			<el-form
				:model="push3Form"
				label-width="120px"
				style="max-width: 800px"
				ref="push3Ref"
				:rules="push3Rules"
			>
				<el-form-item label="标题" :required="true" prop="subject">
					<el-input v-model="push3Form.subject" placeholder="请输入标题" />
				</el-form-item>
				<el-form-item label="推送时间" :required="true">
					<el-form-item prop="push_begin_date">
						<el-date-picker
							v-model="push3Form.push_begin_date"
							type="datetime"
							placeholder="请选择开始时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
						/>
					</el-form-item>
					<span class="split">-</span>
					<el-form-item prop="push_end_date">
						<el-date-picker
							v-model="push3Form.push_end_date"
							type="datetime"
							placeholder="请选择结束时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
						/>
					</el-form-item>
				</el-form-item>
				<el-form-item label="WWW URL" :required="true" prop="url1">
					<el-input v-model="push3Form.url1" placeholder="请入WWW URL" />
				</el-form-item>
				<el-form-item label="颜色" :required="true" prop="color">
					<el-button
						v-for="(color, index) in push3Color"
						:class="push3Form.color == color ? 'color' : color"
						:key="index"
						:color="color"
						size="small"
						@click="push3Form.color = color"
					></el-button>
				</el-form-item>

				<el-form-item>
					<div class="form-submit">
						<el-button type="primary" @click="submitPush3Form(push3Ref)"
							>提交</el-button
						>
						<el-button @click="push3Visible = false">取消</el-button>
					</div>
				</el-form-item>
			</el-form>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import { Edit, Delete, Bottom } from "@element-plus/icons-vue";
import { ref, reactive, onMounted, onUnmounted, nextTick } from "vue";
import type { FormInstance } from "element-plus";
import { VueDraggable } from "vue-draggable-plus";
import { useCool } from "/@/cool";
import { removeEmptyFromObject } from "/@/cool/utils";
import { datelineToDate } from "/@/cool/utils";
import { push3Color } from "../data/index";

const { service } = useCool();

const activeTab = ref("current");

const search = reactive({
	id: ""
});

const tableHeight = ref(0);
const tableHeight2 = ref(0);
const tableData = ref([]);
const tableData2 = ref([]);
const currentPage = ref(1);
const currentPage2 = ref(1);
const pageSize = ref(50);
const pageSize2 = ref(50);
const total = ref(0);
const total2 = ref(0);

const push3AddVisible = ref(false);
const push3AddRef = ref<FormInstance>();
const push3AddForm = reactive({
	url1: "",
	subject: "",
	push_begin_date: "",
	push_end_date: "",
	event_begin_date: "",
	event_end_date: "",
	color: "",
	type: 3
});

const checkColor = (rule: any, value: any, callback: any) => {
	if (push3AddForm.color === "") {
		callback(new Error("请选择颜色"));
	} else {
		callback();
	}
};
const push3AddRules = reactive({
	url1: [{ required: true, message: "请输入WWW URL", trigger: "blur" }],
	subject: [{ required: true, message: "请输入标题", trigger: "blur" }],
	push_begin_date: [{ required: true, message: "请选择开始时间", trigger: "change" }],
	push_end_date: [{ required: true, message: "请选择结束时间", trigger: "change" }],
	color: [{ validator: checkColor, trigger: "blur" }]
});

const submitPush3AddForm = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;

	await formEl.validate((isValid, fields) => {
		if (!isValid) return;

		const obj: any = Object.assign({}, push3AddForm);
		obj.event_begin_date = obj.push_begin_date || 0;
		obj.event_end_date = obj.push_end_date || 0;

		service.base.common.event
			.releaseCreate({
				...obj
			})
			.then((res) => {
				ElMessage({
					message: "修改成功!",
					type: "success"
				});

				push3AddVisible.value = false;
				load();
			})
			.catch((err) => {
				ElMessage.error(err.message);
			});
	});
};

const push3Visible = ref(false);
const push3Ref = ref<FormInstance>();
const push3Form = reactive({
	id: 0,
	url1: "",
	subject: "",
	push_begin_date: "",
	push_end_date: "",
	event_begin_date: "",
	event_end_date: "",
	color: "",
	type: 3
});
const push3Rules = reactive({
	url1: [{ required: true, message: "请输入WWW URL", trigger: "blur" }],
	subject: [{ required: true, message: "请输入标题", trigger: "blur" }],
	push_begin_date: [{ required: true, message: "请选择开始时间", trigger: "blur" }],
	push_end_date: [{ required: true, message: "请选择结束时间", trigger: "blur" }]
});

const submitPush3Form = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;

	await formEl.validate((isValid, fields) => {
		if (!isValid) return;

		const obj = Object.assign({}, push3Form);
		obj.event_begin_date = obj.push_begin_date;
		obj.event_end_date = obj.push_end_date;

		service.base.common.event
			.releaseEditOne({
				...obj
			})
			.then((res) => {
				ElMessage({
					message: "修改成功!",
					type: "success"
				});

				push3Visible.value = false;
				load();
			})
			.catch((err) => {
				ElMessage.error(err.message);
			});
	});
};

const onEnd = () => {
	const obj = {
		ids: tableData.value.map((row: any) => row.id),
		type: 3
	};

	service.base.common.event
		.changeOrder({
			...obj
		})
		.then((res) => {
			ElMessage({
				message: "修改成功!",
				type: "success"
			});

			load();
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const changeColor = (row, color) => {
	row.color = color;

	service.base.common.event
		.releaseEdit({
			...row
		})
		.then((res) => {
			ElMessage({
				message: "修改成功!",
				type: "success"
			});
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const add = () => {
	reset();

	push3AddVisible.value = true;
};

const editClick = (row: any) => {
	push3Visible.value = true;

	getRelease(row);
};

const offlineHandler = (row: any) => {
	service.base.common.event
		.offline({
			id: row.id,
			type: 2
		})
		.then((res) => {
			ElMessage({
				message: "已下线!",
				type: "success"
			});
			refresh();
			refresh2();
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const deleteHandler = (row: any, type) => {
	service.base.common.event
		.releaseDelete({
			id: row.id
		})
		.then((res) => {
			ElMessage({
				message: "已删除!",
				type: "success"
			});
			type === 1 ? refresh() : refresh2();
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};
const handleSizeChange2 = (val: number) => {
	pageSize2.value = val;
	refresh2();
};
const handleCurrentChange2 = (val: number) => {
	currentPage2.value = val;
	refresh2();
};

const getRelease = (row) => {
	service.base.common.event
		.getReleaseOne({ id: row.id })
		.then((res) => {
			push3Form.id = res.id;
			push3Form.url1 = res.url1;
			push3Form.subject = res.subject;
			push3Form.push_begin_date = res.push_begin_date;
			push3Form.push_end_date = res.push_end_date;
			push3Form.color = res.color;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

function reset() {
	push3Form.id = 0;
	push3Form.url1 = "";
	push3Form.subject = "";
	push3Form.push_begin_date = "";
	push3Form.push_end_date = "";
	push3Form.event_begin_date = "";
	push3Form.event_end_date = "";
	push3Form.color = "";

	push3AddForm.url1 = "";
	push3AddForm.subject = "";
	push3AddForm.push_begin_date = "";
	push3AddForm.push_end_date = "";
	push3AddForm.event_begin_date = "";
	push3AddForm.event_end_date = "";
	push3AddForm.color = "";
}

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.event
		.releaseList({
			...s,
			type: 3,
			page: currentPage.value,
			pageSize: pageSize.value
		})
		.then((res) => {
			tableData.value = res[0];
			total.value = res[1];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const refresh2 = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.event
		.releaseList({
			...s,
			status: -1,
			type: 3,
			page: currentPage2.value,
			pageSize: pageSize2.value
		})
		.then((res) => {
			tableData2.value = res[0];
			total2.value = res[1];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const load = () => {
	refresh();
	refresh2();
};

load();

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 290;
		tableHeight2.value = window.innerHeight - 290;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 290;
			tableHeight2.value = window.innerHeight - 290;
		};
	});
});

onUnmounted(() => {
	reset();
});
</script>

<style lang="scss" scoped>
@import "../css/push.scss";
</style>
