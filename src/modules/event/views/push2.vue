<template>
	<div class="wrapper">
		<el-row class="opt">
			<el-col :span="4">
				<el-button type="primary" @click="add">添加</el-button>
			</el-col>
		</el-row>
		<el-row class="list">
			<el-col :span="24">
				<el-tabs v-model="activeTab">
					<el-tab-pane label="当前活动" name="current">
						<div class="cardWraper">
							<el-card class="card">
								<template #header>
									<div class="card-header">
										<span>LEVEL 1</span>
									</div>
								</template>

								<draggable
									v-model="level1"
									group="level"
									class="scrollbar-flex-content"
									:sort="true"
									@end="onEnd"
								>
									<template #item="{ element }">
										<div class="scrollbar-item cursor-pointer">
											<img
												:src="element.image"
												width="200"
												height="150"
												:alt="element.subject"
											/>
											<p>
												{{
													datelineToDate(
														element.push_begin_date,
														"YYYY-MM-DD HH:mm"
													)
												}}
												<br />
												{{
													datelineToDate(
														element.push_end_date,
														"YYYY-MM-DD HH:mm"
													)
												}}
											</p>
											<el-popover
												v-model="popoverVisibility[element.id]"
												trigger="click"
												width="80"
												popper-style="box-shadow: rgb(14 18 22 / 35%) 0px 10px 38px -10px, rgb(14 18 22 / 20%) 0px 10px 20px -15px; padding: 20px;"
											>
												<template #reference>
													<div class="more">
														<el-icon><MoreFilled /></el-icon>
													</div>
												</template>
												<template #default>
													<el-row class="links">
														<el-col :span="24">
															<el-button
																:icon="Link"
																class="btn"
																type="primary"
																plain
																@click="moreClick(1, element)"
																>链接</el-button
															>
															<el-button
																:icon="Edit"
																class="btn"
																type="success"
																plain
																@click="moreClick(2, element)"
																>编辑</el-button
															>
															<el-button
																:icon="Bottom"
																class="btn"
																type="info"
																plain
																@click="moreClick(4, element)"
																>下线</el-button
															>
															<el-button
																:icon="Delete"
																class="btn"
																type="danger"
																plain
																@click="moreClick(3, element)"
																>删除</el-button
															></el-col
														>
													</el-row>
												</template>
											</el-popover>
										</div>
									</template>
								</draggable>
							</el-card>

							<el-card class="card">
								<template #header>
									<div class="card-header">
										<span>LEVEL 2</span>
									</div>
								</template>

								<draggable
									v-model="level2"
									group="level"
									class="scrollbar-flex-content"
									:sort="true"
									@end="onEnd"
								>
									<template #item="{ element }">
										<div class="scrollbar-item cursor-pointer">
											<img
												:src="element.image"
												width="200"
												height="150"
												:alt="element.subject"
											/>
											<p>
												{{
													datelineToDate(
														element.push_begin_date,
														"YYYY-MM-DD HH:mm"
													)
												}}
												<br />
												{{
													datelineToDate(
														element.push_end_date,
														"YYYY-MM-DD HH:mm"
													)
												}}
											</p>
											<el-popover
												v-model="popoverVisibility[element.id]"
												trigger="click"
												width="80"
												popper-style="box-shadow: rgb(14 18 22 / 35%) 0px 10px 38px -10px, rgb(14 18 22 / 20%) 0px 10px 20px -15px; padding: 20px;"
											>
												<template #reference>
													<div class="more">
														<el-icon><MoreFilled /></el-icon>
													</div>
												</template>
												<template #default>
													<el-row class="links">
														<el-col :span="24">
															<el-button
																:icon="Link"
																class="btn"
																type="primary"
																plain
																@click="moreClick(1, element)"
																>链接</el-button
															>
															<el-button
																:icon="Edit"
																class="btn"
																type="success"
																plain
																@click="moreClick(2, element)"
																>编辑</el-button
															>
															<el-button
																:icon="Bottom"
																class="btn"
																type="info"
																plain
																@click="moreClick(4, element)"
																>下线</el-button
															>
															<el-button
																:icon="Delete"
																class="btn"
																type="danger"
																plain
																@click="moreClick(3, element)"
																>删除</el-button
															></el-col
														>
													</el-row>
												</template>
											</el-popover>
										</div>
									</template>
								</draggable>
							</el-card>

							<el-card class="card">
								<template #header>
									<div class="card-header">
										<span>LEVEL 3</span>
									</div>
								</template>

								<draggable
									v-model="level3"
									group="level"
									class="scrollbar-flex-content"
									:sort="true"
									@end="onEnd"
								>
									<template #item="{ element }">
										<div class="scrollbar-item cursor-pointer">
											<img
												:src="element.image"
												width="200"
												height="150"
												:alt="element.subject"
											/>
											<p>
												{{
													datelineToDate(
														element.push_begin_date,
														"YYYY-MM-DD HH:mm"
													)
												}}
												<br />
												{{
													datelineToDate(
														element.push_end_date,
														"YYYY-MM-DD HH:mm"
													)
												}}
											</p>
											<el-popover
												v-model="popoverVisibility[element.id]"
												trigger="click"
												width="80"
												popper-style="box-shadow: rgb(14 18 22 / 35%) 0px 10px 38px -10px, rgb(14 18 22 / 20%) 0px 10px 20px -15px; padding: 20px;"
											>
												<template #reference>
													<div class="more">
														<el-icon><MoreFilled /></el-icon>
													</div>
												</template>
												<template #default>
													<el-row class="links">
														<el-col :span="24">
															<el-button
																:icon="Link"
																class="btn"
																type="primary"
																plain
																@click="moreClick(1, element)"
																>链接</el-button
															>
															<el-button
																:icon="Edit"
																class="btn"
																type="success"
																plain
																@click="moreClick(2, element)"
																>编辑</el-button
															>
															<el-button
																:icon="Bottom"
																class="btn"
																type="info"
																plain
																@click="moreClick(4, element)"
																>下线</el-button
															>
															<el-button
																:icon="Delete"
																class="btn"
																type="danger"
																plain
																@click="moreClick(3, element)"
																>删除</el-button
															></el-col
														>
													</el-row>
												</template>
											</el-popover>
										</div>
									</template>
								</draggable>
							</el-card>
						</div>
					</el-tab-pane>
					<el-tab-pane label="已结束活动" name="completed">
						<el-table
							border
							style="width: 100%"
							:data="tableData"
							:max-height="tableHeight"
							:row-style="{ height: '40px' }"
							:header-cell-style="{ background: '#ebeef5', color: '#333' }"
						>
							<el-table-column label="ID" prop="id" width="90" align="center">
							</el-table-column>
							<el-table-column
								label="标题"
								:show-overflow-tooltip="true"
								align="left"
							>
								<template #default="scope">
									<el-button type="primary" link @click="open(scope.row)">
										{{ scope.row.subject }}
									</el-button>
								</template>
							</el-table-column>
							<el-table-column label="最新标签时间" width="180" align="center">
								<template #default="scope">
									{{ datelineToDate(scope.row.event_end_date) }}
								</template>
							</el-table-column>

							<el-table-column fixed="right" label="操作" width="100" align="center">
								<template #default="scope">
									<el-icon
										@click="editClick(scope.row)"
										class="cursor-pointer"
										style="color: #464bd7; margin-right: 8px"
										:size="20"
										><Edit
									/></el-icon>
								</template>
							</el-table-column>
						</el-table>
					</el-tab-pane>
				</el-tabs>
			</el-col>
		</el-row>
		<el-row class="page">
			<el-pagination
				v-if="activeTab == 'completed'"
				class="pagination"
				background
				:currentPage="currentPage"
				:page-size="pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</el-row>

		<el-dialog v-model="push2AddVisible" title="添加 推2" width="1000">
			<el-form
				:model="push2AddForm"
				label-width="120px"
				style="max-width: 1000px"
				ref="push2AddRef"
				:rules="push2AddRules"
			>
				<div class="locationWrap">
					<el-form-item label="活动标题" :required="true" prop="subject">
						<el-input v-model="push2AddForm.subject" placeholder="请输入活动标题" />
					</el-form-item>
					<el-form-item label="推送时间" :required="true">
						<el-date-picker
							v-model="push2AddForm.push_begin_date"
							type="datetime"
							placeholder="请选择开始时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
						/>
						<span class="split">-</span>
						<el-date-picker
							v-model="push2AddForm.push_end_date"
							type="datetime"
							placeholder="请选择结束时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
						/>
					</el-form-item>
					<el-form-item label="WWW URL" :required="true" prop="url1">
						<el-input v-model="push2AddForm.url1" placeholder="请入WWW URL" />
					</el-form-item>
					<el-form-item label="上传图片" :required="true" prop="image">
						<div v-if="push2AddForm.image?.length > 0" class="material">
							<el-image
								style="width: 100px; height: 100px"
								:src="push2AddForm.image"
								fit="cover"
							/>
							<el-icon class="del" size="20" @click="delMaterial()"
								><Delete
							/></el-icon>
						</div>
						<el-icon
							v-else
							class="cursor-pointer"
							size="30"
							color="#CFD3DC"
							@click="addMaterial()"
							><CirclePlus
						/></el-icon>
						<input
							type="file"
							multiple
							accept="image/*"
							:ref="setRefs('fileInput')"
							style="display: none"
							@change="handleFileChange($event, 1)"
						/>
					</el-form-item>
				</div>

				<el-form-item>
					<div class="form-submit">
						<el-button type="primary" @click="submitPush2AddForm(push2AddRef)"
							>提交</el-button
						>
						<el-button @click="push2AddVisible = false">取消</el-button>
					</div>
				</el-form-item>
			</el-form>
		</el-dialog>

		<el-dialog v-model="push2Visible" title="编辑 推2" width="800">
			<el-form
				:model="push2Form"
				label-width="120px"
				style="max-width: 800px"
				ref="push2Ref"
				:rules="push2Rules"
			>
				<el-form-item label="活动标题" :required="true" prop="subject">
					<el-input v-model="push2Form.subject" placeholder="请输入活动标题" />
				</el-form-item>
				<el-form-item label="推送时间" :required="true">
					<el-date-picker
						v-model="push2Form.push_begin_date"
						type="datetime"
						placeholder="请选择开始时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
					/>
					<span class="split">-</span>
					<el-date-picker
						v-model="push2Form.push_end_date"
						type="datetime"
						placeholder="请选择结束时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
					/>
				</el-form-item>
				<el-form-item label="WWW URL" :required="true" prop="url1">
					<el-input v-model="push2Form.url1" placeholder="请入WWW URL" />
				</el-form-item>
				<el-form-item label="上传图片" :required="true" prop="image">
					<div v-if="push2Form.image?.length > 0" class="material">
						<el-image
							style="width: 100px; height: 100px"
							:src="push2Form.image"
							fit="cover"
						/>
						<el-icon class="del" size="20" @click="delMaterial()"><Delete /></el-icon>
					</div>
					<el-icon
						v-else
						class="cursor-pointer"
						size="30"
						color="#CFD3DC"
						@click="addMaterial()"
						><CirclePlus
					/></el-icon>
					<input
						type="file"
						multiple
						accept="image/*"
						:ref="setRefs('fileInput')"
						style="display: none"
						@change="handleFileChange($event, 2)"
					/>
				</el-form-item>

				<el-form-item>
					<div class="form-submit">
						<el-button type="primary" @click="submitPush2Form(push2Ref)"
							>提交</el-button
						>
						<el-button @click="push2Visible = false">取消</el-button>
					</div>
				</el-form-item>
			</el-form>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import { Edit, Delete, CirclePlus, MoreFilled, Link, Bottom } from "@element-plus/icons-vue";
import { ref, reactive, onMounted, onUnmounted, nextTick } from "vue";
import draggable from "vuedraggable";
import type { FormInstance } from "element-plus";
import { useCool } from "/@/cool";
import { removeEmptyFromObject } from "/@/cool/utils";
import { datelineToDate } from "/@/cool/utils";
import { isDev } from "/@/config";

const { service, refs, setRefs } = useCool();

const domain = isDev ? "http://127.0.0.1:8080" : "https://static.chasedream.com";
const activeTab = ref("current");

const search = reactive({
	id: ""
});

const tableHeight = ref(0);
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(50);
const total = ref(0);

const level1 = ref([]);
const level2 = ref([]);
const level3 = ref([]);
const popoverVisibility = ref({});

const moreClick = (type, el) => {
	if (type === 1) {
		window.open(el.url1);
	} else if (type === 2) {
		editClick(el);
		popoverVisibility.value[el.id] = false;
	} else if (type === 3) {
		service.base.common.event
			.releaseDelete({
				id: el.id
			})
			.then((res) => {
				ElMessage({
					message: "已删除!",
					type: "success"
				});

				popoverVisibility.value[el.id] = false;
				load();
			})
			.catch((err) => {
				ElMessage.error(err.message);
			});
	} else if (type === 4) {
		service.base.common.event
			.offline({
				id: el.id,
				type: 2
			})
			.then((res) => {
				ElMessage({
					message: "已下线!",
					type: "success"
				});

				popoverVisibility.value[el.id] = false;
				load();
			})
			.catch((err) => {
				ElMessage.error(err.message);
			});
	}
};

const push2Visible = ref(false);
const push2Ref = ref<FormInstance>();
const push2Form = reactive({
	id: 0,
	url1: "",
	subject: "",
	push_begin_date: "",
	push_end_date: "",
	event_begin_date: "",
	event_end_date: "",
	image: ""
});
const push2Rules = reactive({
	subject: [{ required: true, message: "请输入标题", trigger: "blur" }],
	url1: [{ required: true, message: "请输入WWW URL", trigger: "blur" }],
	push_begin_date: [{ required: true, message: "推送时间开始时间", trigger: "blur" }],
	push_end_date: [{ required: true, message: "推送时间结束时间", trigger: "blur" }]
});

const submitPush2Form = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;

	await formEl.validate((isValid, fields) => {
		if (!isValid) return;

		const obj = Object.assign({}, push2Form);
		obj.event_begin_date = obj.push_begin_date;
		obj.event_end_date = obj.push_end_date;

		service.base.common.event
			.releaseEditOne({
				...obj
			})
			.then((res) => {
				ElMessage({
					message: "修改成功!",
					type: "success"
				});

				push2Visible.value = false;
				load();
			})
			.catch((err) => {
				ElMessage.error(err.message);
			});
	});
};

const push2AddVisible = ref(false);
const push2AddRef = ref<FormInstance>();
const push2AddForm = reactive({
	type: 2,
	level: 1,
	subject: "",
	url1: "",
	push_begin_date: "",
	push_end_date: "",
	event_begin_date: "",
	event_end_date: "",
	image: ""
});
const push2AddRules = reactive({
	subject: [{ required: true, message: "请输入标题", trigger: "blur" }],
	url1: [{ required: true, message: "请输入WWW URL", trigger: "blur" }],
	push_begin_date: [{ required: true, message: "推送时间开始时间", trigger: "blur" }],
	push_end_date: [{ required: true, message: "推送时间结束时间", trigger: "blur" }]
});

const submitPush2AddForm = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;

	await formEl.validate((isValid, fields) => {
		if (!isValid) return;

		const obj = Object.assign({}, push2AddForm);
		obj.event_begin_date = obj.push_begin_date;
		obj.event_end_date = obj.push_end_date;

		service.base.common.event
			.releaseCreate({
				...obj
			})
			.then((res) => {
				ElMessage({
					message: "修改成功!",
					type: "success"
				});

				push2AddVisible.value = false;
				load();
			})
			.catch((err) => {
				ElMessage.error(err.message);
			});
	});
};

const onEnd = () => {
	const obj = {
		data: [
			{
				level: 1,
				ids: level1.value.map((el: any) => el.id) || []
			},
			{
				level: 2,
				ids: level2.value.map((el: any) => el.id) || []
			},
			{
				level: 3,
				ids: level3.value.map((el: any) => el.id) || []
			}
		],
		type: 2
	};

	service.base.common.event
		.changeOrder({
			...obj
		})
		.then((res) => {
			ElMessage({
				message: "修改成功!",
				type: "success"
			});

			load();
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const add = () => {
	reset();

	push2AddVisible.value = true;
};

const open = (row: any) => {
	window.open(row.url1);
};

const addMaterial = () => {
	const el = refs[`fileInput`];
	el?.click();
};

const delMaterial = () => {
	push2Form.image = "";
};

const handleFileChange = async (event: any, type) => {
	const files = event.target.files;
	if (files) {
		await upload(files, type);
	}

	event.target.value = "";
};

const upload = async (files: any, type) => {
	for (let i = 0; i < files.length; i++) {
		const formData = new FormData();
		formData.append("files", files[i]);

		try {
			const url = isDev
				? "http://localhost:9000/dev/admin/base/open/upload4EventPush2"
				: "https://connect.chasedream.com/api/v2/admin/base/open/upload4EventPush2";

			const response = await fetch(url, {
				method: "POST",
				body: formData
			});

			if (response.ok) {
				const res = await response.json();
				if (type === 1) {
					push2AddForm.image = res.data.image;
				} else if (type === 2) {
					push2Form.image = res.data.image;
				}
			} else {
				ElMessage.error("Upload failed");
			}
		} catch (err: any) {
			ElMessage.error(err.message);
		}
	}
};

const editClick = (row: any) => {
	push2Visible.value = true;

	getRelease(row);
};

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh2();
};
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh2();
};

const getRelease = (row) => {
	service.base.common.event
		.getReleaseOne({ id: row.id })
		.then((res) => {
			push2Form.id = res.id;
			push2Form.url1 = res.url1;
			push2Form.subject = res.subject;
			push2Form.push_begin_date = res.push_begin_date;
			push2Form.push_end_date = res.push_end_date;
			push2Form.image = res.image;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

function reset() {
	push2Form.id = 0;
	push2Form.subject = "";
	push2Form.url1 = "";
	push2Form.push_begin_date = "";
	push2Form.push_end_date = "";
	push2Form.event_begin_date = "";
	push2Form.event_end_date = "";
	push2Form.image = "";

	push2AddForm.subject = "";
	push2AddForm.url1 = "";
	push2AddForm.push_begin_date = "";
	push2AddForm.push_end_date = "";
	push2AddForm.event_begin_date = "";
	push2AddForm.event_end_date = "";
	push2AddForm.image = "";
}

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.event
		.push2ComingSoon({})
		.then((res) => {
			level1.value = res.level1;
			level2.value = res.level2;
			level3.value = res.level3;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};
const refresh2 = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.event
		.releaseList({
			...s,
			status: -1,
			type: 2,
			page: currentPage.value,
			pageSize: pageSize.value
		})
		.then((res) => {
			tableData.value = res[0];
			total.value = res[1];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const load = () => {
	refresh();
	refresh2();
};

load();

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 290;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 290;
		};
	});
});

onUnmounted(() => {
	reset();
});
</script>

<style lang="scss" scoped>
@import "../css/push.scss";
</style>
