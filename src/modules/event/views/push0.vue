<template>
	<div class="wrapper">
		<el-row class="list">
			<el-col :span="24">
				<el-tabs v-model="activeTab">
					<el-tab-pane label="当前活动" name="current">
						<el-table
							border
							style="width: 100%"
							:data="tableData"
							:max-height="tableHeight"
							:row-style="{ height: '40px' }"
							:header-cell-style="{ background: '#ebeef5', color: '#333' }"
						>
							<el-table-column width="100" label="日期" align="center">
								<template #default="scope">
									<div>
										<div style="position: relative; height: 45px; width: 50px">
											<span
												style="
													position: absolute;
													left: 8px;
													top: 0px;
													font-size: 10px;
													font-weight: bold;
													color: rgb(217, 52, 52);
												"
												>{{ formatMonth(scope.row.event_begin_date) }}</span
											><span
												class="line"
												style="
													position: absolute;
													width: 35px;
													height: 40px;
												"
											></span
											><span
												style="
													position: absolute;
													right: 0px;
													bottom: 0px;
													font-size: 20px;
													font-weight: bold;
													color: rgb(217, 52, 52);
												"
												>{{ formatDay(scope.row.event_begin_date) }}</span
											>
										</div>
										<span style="font-size: 12px">{{
											formatWeek(scope.row.event_begin_date)
										}}</span>
									</div>
								</template>
							</el-table-column>
							<el-table-column label="logo" width="90" align="center">
								<template #default="scope">
									<el-image
										style="width: 60px; height: 60px"
										:src="scope.row.image"
										fit="cover"
									/>
								</template>
							</el-table-column>
							<el-table-column
								prop="subject"
								label="标题"
								:show-overflow-tooltip="true"
								align="left"
							>
							</el-table-column>
							<el-table-column label="展示/开始结束时间" width="180" align="center">
								<template #default="scope">
									{{ datelineToDate(scope.row.push_begin_date) }}
									{{ datelineToDate(scope.row.push_end_date) }}
								</template>
							</el-table-column>
							<el-table-column label="活动/开始结束时间	" width="180" align="center">
								<template #default="scope">
									{{ datelineToDate(scope.row.event_begin_date) }}
									{{ datelineToDate(scope.row.event_end_date) }}
								</template>
							</el-table-column>
							<el-table-column label="最新标签时间" width="180" align="center">
								<template #default="scope">
									{{ datelineToDate(scope.row.new_flag_date) }}
								</template>
							</el-table-column>

							<el-table-column fixed="right" label="操作" width="110" align="center">
								<template #default="scope">
									<el-icon
										@click="editClick(scope.row)"
										class="cursor-pointer"
										style="color: #464bd7; margin-right: 8px"
										:size="20"
										><Edit
									/></el-icon>

									<el-popconfirm
										title="确定要下线吗?"
										@confirm="offlineHandler(scope.row)"
									>
										<template #reference>
											<el-icon
												class="cursor-pointer"
												style="color: lightseagreen; margin-right: 8px"
												:size="20"
												><bottom
											/></el-icon>
										</template>
									</el-popconfirm>

									<el-popconfirm
										title="确定要删除吗?"
										@confirm="deleteHandler(scope.row, 1)"
									>
										<template #reference>
											<el-icon
												class="cursor-pointer"
												style="color: crimson; margin-right: 8px"
												:size="20"
												><delete
											/></el-icon>
										</template>
									</el-popconfirm>
								</template>
							</el-table-column>
						</el-table>
					</el-tab-pane>
					<el-tab-pane label="已结束活动" name="completed">
						<el-table
							border
							style="width: 100%"
							:data="tableData2"
							:max-height="tableHeight"
							:row-style="{ height: '40px' }"
							:header-cell-style="{ background: '#ebeef5', color: '#333' }"
						>
							<el-table-column width="100" label="日期" align="center">
								<template #default="scope">
									<div>
										<div style="position: relative; height: 45px; width: 50px">
											<span
												style="
													position: absolute;
													left: 8px;
													top: 0px;
													font-size: 10px;
													font-weight: bold;
													color: rgb(217, 52, 52);
												"
												>{{ formatMonth(scope.row.event_begin_date) }}</span
											><span
												class="line"
												style="
													position: absolute;
													width: 35px;
													height: 40px;
												"
											></span
											><span
												style="
													position: absolute;
													right: 0px;
													bottom: 0px;
													font-size: 20px;
													font-weight: bold;
													color: rgb(217, 52, 52);
												"
												>{{ formatDay(scope.row.event_begin_date) }}</span
											>
										</div>
										<span style="font-size: 12px">{{
											formatWeek(scope.row.event_begin_date)
										}}</span>
									</div>
								</template>
							</el-table-column>
							<el-table-column label="logo" width="90" align="center">
								<template #default="scope">
									<el-image
										style="width: 60px; height: 60px"
										:src="scope.row.image"
										fit="fit"
									/>
								</template>
							</el-table-column>
							<el-table-column
								prop="subject"
								label="标题"
								:show-overflow-tooltip="true"
								align="left"
							>
							</el-table-column>
							<el-table-column label="展示/开始结束时间" width="180" align="center">
								<template #default="scope">
									{{ datelineToDate(scope.row.push_begin_date) }}
									{{ datelineToDate(scope.row.push_end_date) }}
								</template>
							</el-table-column>
							<el-table-column label="活动/开始结束时间	" width="180" align="center">
								<template #default="scope">
									{{ datelineToDate(scope.row.event_begin_date) }}
									{{ datelineToDate(scope.row.event_end_date) }}
								</template>
							</el-table-column>
							<el-table-column label="最新标签时间" width="180" align="center">
								<template #default="scope">
									{{ datelineToDate(scope.row.new_flag_date) }}
								</template>
							</el-table-column>

							<el-table-column fixed="right" label="操作" width="100" align="center">
								<template #default="scope">
									<el-popconfirm
										title="确定要删除吗?"
										@confirm="deleteHandler(scope.row, 2)"
									>
										<template #reference>
											<el-icon
												class="cursor-pointer"
												style="color: crimson; margin-right: 8px"
												:size="20"
												><delete
											/></el-icon>
										</template>
									</el-popconfirm>
								</template>
							</el-table-column>
						</el-table>
					</el-tab-pane>
				</el-tabs>
			</el-col>
		</el-row>
		<el-row class="page">
			<el-pagination
				v-if="activeTab == 'current'"
				class="pagination"
				background
				:currentPage="currentPage"
				:page-size="pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
			<el-pagination
				v-else
				class="pagination"
				background
				:currentPage="currentPage2"
				:page-size="pageSize2"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total2"
				@size-change="handleSizeChange2"
				@current-change="handleCurrentChange2"
			/>
		</el-row>

		<el-dialog v-model="push0Visible" title="推0" width="1000">
			<el-form
				:model="push0Form"
				label-width="120px"
				style="max-width: 1000px"
				ref="push0Ref"
				:rules="push0Rules"
			>
				<el-form-item label="机构" :required="true">
					<el-select v-model="push0Form.school_id" placeholder="请选择学校" disabled>
						<el-option
							v-for="item in organizations"
							:key="item.id"
							:label="item.display_name"
							:value="item.id"
						>
							<span style="float: left">{{ item.display_name }}</span>
							<span
								style="
									float: right;
									color: var(--el-text-color-secondary);
									font-size: 13px;
								"
							>
								{{ item.country }}
							</span>
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="WWW URL" :required="true" prop="url1">
					<el-input v-model="push0Form.url1" placeholder="请入WWW URL" />
				</el-form-item>
				<el-form-item label="论坛 URL" :required="true" prop="url2">
					<el-input v-model="push0Form.url2" placeholder="请入论坛 URL" />
				</el-form-item>
				<div class="locationWrap">
					<el-form-item label="活动地区">
						<el-row>
							<el-checkbox :checked="true" disabled>{{
								push0Form?.geo?.name
							}}</el-checkbox>
						</el-row>
					</el-form-item>
					<el-form-item label="专业" :required="true">
						<el-checkbox-group v-model="push0Form.school_major" size="small" disabled>
							<el-checkbox
								v-for="obj in school_major"
								:key="obj.id"
								:label="obj.short"
								:value="obj.id"
							/>
						</el-checkbox-group>
					</el-form-item>
					<el-form-item label="活动类型" :required="true">
						<el-checkbox-group v-model="push0Form.event_type" size="small" disabled>
							<el-checkbox
								v-for="obj in event_type"
								:key="obj.id"
								:label="obj.name"
								:value="obj.id"
							/>
						</el-checkbox-group>
					</el-form-item>
					<el-form-item label="活动标题" :required="true" prop="subject">
						<el-input v-model="push0Form.subject" placeholder="请输入活动标题" />
					</el-form-item>
					<div style="padding-left: 125px">
						<strong style="width: 275px; display: inline-block">活动开始时间</strong>
						<strong style="width: 275px; display: inline-block">活动结束时间</strong>
					</div>
					<el-form-item label="活动时间">
						<el-date-picker
							v-model="push0Form.event_begin_date"
							type="datetime"
							placeholder="请选择开始时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
							disabled
						/>
						<span class="split">-</span>
						<el-date-picker
							v-model="push0Form.event_end_date"
							type="datetime"
							placeholder="请选择结束时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
							disabled
						/>
						<el-checkbox
							v-model="push0Form.multi_day"
							style="margin-left: 10px"
							label="跨天"
							true-value="1"
							false-value="0"
							disabled
						/>
					</el-form-item>
					<div style="padding-left: 125px">
						<strong style="width: 275px; display: inline-block">推送开始时间</strong>
						<strong style="width: 275px; display: inline-block">推送结束时间</strong>
					</div>
					<el-form-item label="推送时间">
						<el-date-picker
							v-model="push0Form.push_begin_date"
							type="datetime"
							placeholder="请选择开始时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
						/>
						<span class="split">-</span>
						<el-date-picker
							v-model="push0Form.push_end_date"
							type="datetime"
							placeholder="请选择结束时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
						/>
					</el-form-item>
					<el-form-item label="最新标签下线时间" :required="true" prop="new_flag_date">
						<el-date-picker
							v-model="push0Form.new_flag_date"
							type="datetime"
							placeholder="请选择开始时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
						/>
					</el-form-item>
					<el-form-item label="活动排序" :required="true" size="small" prop="position">
						<el-radio-group v-model="push0Form.position" class="ml-4">
							<el-radio value="1">置顶</el-radio>
							<el-radio value="0">正常</el-radio>
							<el-radio value="-1">沉底</el-radio>
						</el-radio-group>
					</el-form-item>
					<el-form-item label="上传图片">
						<div v-if="push0Form.image?.length > 0" class="material">
							<el-image
								style="width: 100px; height: 100px"
								:src="push0Form.image"
								fit="cover"
							/>
							<el-icon class="del" size="20" @click="delMaterial()"
								><Delete
							/></el-icon>
						</div>
						<el-icon
							v-else
							class="cursor-pointer"
							size="30"
							color="#CFD3DC"
							@click="addMaterial()"
							><CirclePlus
						/></el-icon>
						<input
							type="file"
							multiple
							accept="image/*"
							:ref="setRefs('fileInput')"
							style="display: none"
							@change="handleFileChange($event)"
						/>
					</el-form-item>
				</div>

				<el-form-item>
					<div class="form-submit">
						<el-button type="primary" @click="submitPush0Form(push0Ref)"
							>提交</el-button
						>
						<el-button @click="push0Visible = false">取消</el-button>
					</div>
				</el-form-item>
			</el-form>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import { Edit, Delete, CirclePlus, Bottom } from "@element-plus/icons-vue";
import { ref, reactive, onMounted, nextTick } from "vue";
import type { FormInstance } from "element-plus";
import { useCool } from "/@/cool";
import { removeEmptyFromObject } from "/@/cool/utils";
import { datelineToDate } from "/@/cool/utils";
import { isDev } from "/@/config";

const { service, refs, setRefs } = useCool();

const domain = isDev ? "http://127.0.0.1:8080" : "https://static.chasedream.com";
const activeTab = ref("current");

const search = reactive({
	id: "",
	subject: ""
});

const tableHeight = ref(0);
const tableHeight2 = ref(0);
const tableData = ref([]);
const tableData2 = ref([]);
const currentPage = ref(1);
const currentPage2 = ref(1);
const pageSize = ref(50);
const pageSize2 = ref(50);
const total = ref(0);
const total2 = ref(0);

const organizations = ref([]);
const event_type = ref([]);
const countries = ref([]);
const regions = ref([]);
const cities = ref([]);
const school_major = ref([]);

const push0Visible = ref(false);
const push0Ref = ref<FormInstance>();
const push0Form = reactive({
	eventId: 0,
	url1: "",
	url2: "",
	school_id: "",
	subject: "",
	location_id: "",
	lid: "",
	school_major: [],
	event_type: [],
	event_begin_date: "",
	event_end_date: "",
	push_begin_date: "",
	push_end_date: "",
	new_flag_date: "",
	position: "",
	multi_day: "",
	image: ""
});
const push0Rules = reactive({
	url1: [{ required: true, message: "请输入WWW URL", trigger: "blur" }],
	url2: [{ required: true, message: "请输入论坛 URL", trigger: "blur" }],
	subject: [{ required: true, message: "请输入标题", trigger: "blur" }],
	position: [{ required: true, message: "请选择活动排序", trigger: "blur" }],
	new_flag_date: [{ required: true, message: "最新标签下线时间", trigger: "blur" }]
});

const addMaterial = () => {
	const el = refs[`fileInput`];
	el?.click();
};

const delMaterial = () => {
	push0Form.image = "";
};

const handleFileChange = async (event: any) => {
	const files = event.target.files;
	if (files) {
		await upload(files);
	}

	event.target.value = "";
};

const submitPush0Form = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;

	await formEl.validate((isValid, fields) => {
		if (!isValid) return;

		const org = organizations.value.find((el: any) => el.id == push0Form.school_id);
		push0Form.image =
			push0Form.image ||
			`https://static.chasedream.com/events/event-IMG/${org.directory}/logo/1024.png`;

		service.base.common.event
			.calendarEditOne({
				...push0Form
			})
			.then((res) => {
				ElMessage({
					message: "修改成功!",
					type: "success"
				});

				push0Visible.value = false;
				load();
			})
			.catch((err) => {
				ElMessage.error(err.message);
			});
	});
};

const upload = async (files: any) => {
	for (let i = 0; i < files.length; i++) {
		const formData = new FormData();
		formData.append("files", files[i]);

		try {
			const baseUrl = isDev
				? "http://localhost:9000/dev/admin/base/open/upload4Event"
				: "https://connect.chasedream.com/api/v2/admin/base/open/upload4Event";

			const params = new URLSearchParams({
				school_id: push0Form.school_id,
				type: "0"
			});

			const url = `${baseUrl}?${params.toString()}`;

			const response = await fetch(url, {
				method: "POST",
				body: formData
			});

			if (response.ok) {
				const res = await response.json();
				push0Form.image = res.data.image;
			} else {
				ElMessage.error("Upload failed");
			}
		} catch (err: any) {
			ElMessage.error(err.message);
		}
	}
};

const formatDay = (timestamp) => {
	const date = new Date(timestamp * 1000);
	const day = date.getDate();
	return day < 10 ? `0${day}` : `${day}`;
};

const formatMonth = (timestamp) => {
	console.log(timestamp);
	const date = new Date(timestamp * 1000);
	const monthNames = [
		"JAN",
		"FEB",
		"MAR",
		"APR",
		"MAY",
		"JUN",
		"JUL",
		"AUG",
		"SEP",
		"OCT",
		"NOV",
		"DEC"
	];
	return monthNames[date.getMonth()];
};

const formatWeek = (timestamp) => {
	const date = new Date(timestamp * 1000);
	const weekdays = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
	return weekdays[date.getDay()];
};

const editClick = (row: any) => {
	push0Visible.value = true;
	push0Form.eventId = row.event_id;
	loadOrgs();
	loadEventTypes();
	loadGeo("", "countries");
	loadEvent(row);
};

const offlineHandler = (row: any) => {
	service.base.common.event
		.offline({
			id: row.id,
			type: 1
		})
		.then((res) => {
			ElMessage({
				message: "已下线!",
				type: "success"
			});
			refresh();
			refresh2();
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const deleteHandler = (row: any, type) => {
	service.base.common.event
		.calendarDelete({
			id: row.id
		})
		.then((res) => {
			ElMessage({
				message: "已删除!",
				type: "success"
			});
			type === 1 ? refresh() : refresh2();
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};
const handleSizeChange2 = (val: number) => {
	pageSize2.value = val;
	refresh2();
};
const handleCurrentChange2 = (val: number) => {
	currentPage2.value = val;
	refresh2();
};

const loadOrgs = () => {
	service.base.common.event
		.orgs({})
		.then((res) => {
			organizations.value = res;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const loadEventTypes = () => {
	service.base.common.event
		.type({})
		.then((res) => {
			event_type.value = res;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const loadGeo = (parent_id = "", target = "") => {
	service.base.common.event
		.geo({ parent_id })
		.then((res) => {
			if (target === "countries") {
				countries.value = res;
			} else if (target === "regions") {
				regions.value = res;
			} else if (target === "cities") {
				cities.value = res;
			}
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const loadSchoolMajor = (school_id) => {
	service.base.common.event
		.school_major({
			school_id
		})
		.then((res) => {
			school_major.value = res;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const getCalendar = (row) => {
	service.base.common.event
		.getCalendar({ id: row.id })
		.then((res) => {
			Object.assign(push0Form, res);
			push0Form.position = push0Form.position.toString();
			push0Form.school_id = push0Form.school_id.toString();

			push0Form.multi_day =
				push0Form.event_end_date - push0Form.event_begin_date >= 60 * 60 * 24 ? "1" : "0";
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const loadEvent = (row) => {
	const eventId = row.eventId;
	service.base.common.event
		.core({ eventId })
		.then((res) => {
			push0Form.school_id = res.school_id.toString();

			loadSchoolMajor(push0Form.school_id);
			getCalendar(row);
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.event
		.calendarList({
			...s,
			page: currentPage.value,
			pageSize: pageSize.value
		})
		.then((res) => {
			tableData.value = res[0];
			total.value = res[1];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};
const refresh2 = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.event
		.calendarList({
			...s,
			status: -1,
			page: currentPage2.value,
			pageSize: pageSize2.value
		})
		.then((res) => {
			tableData2.value = res[0];
			total2.value = res[1];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const load = () => {
	refresh();
	refresh2();
};

load();

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 290;
		tableHeight2.value = window.innerHeight - 290;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 290;
			tableHeight2.value = window.innerHeight - 290;
		};
	});
});
</script>

<style lang="scss" scoped>
@import "../css/push.scss";
</style>
