<template>
	<div class="wrapper">
		<div class="form-container">
			<el-form :model="form" label-width="100px" :rules="rules" ref="formRef">
				<el-form-item label="机构" :required="true" prop="school_id">
					<el-select
						v-model="form.school_id"
						placeholder="请选择学校"
						filterable
						@change="orgChange"
					>
						<el-option
							v-for="item in organizations"
							:key="item.id"
							:label="item.display_name"
							:value="item.id"
						>
							<span style="float: left">{{ item.display_name }}</span>
							<span
								style="
									float: right;
									color: var(--el-text-color-secondary);
									font-size: 13px;
								"
							>
								{{ item.country }}
							</span>
						</el-option>
					</el-select>
				</el-form-item>

				<el-form-item label="素材">
					<div
						v-for="obj in materials"
						:key="obj.id"
						class="material cursor-pointer"
						@click="copyClick(obj)"
					>
						<el-image
							style="width: 45px; height: 45px"
							:src="domain + obj.fullpath"
							fit="fit"
						/>
						<div class="copy">复制</div>
					</div>
					<el-button @click="materialClick" link>...</el-button>

					<el-dialog v-model="dialogVisible" title="素材库" width="500">
						<div class="material-new">
							<div class="item" v-for="(obj, index) in materialNews" :key="index">
								<img :src="domain + obj.fullpath" @click="copyClick(obj)" />
								<el-icon class="icon" size="15" @click="delMaterial(obj, index)"
									><CloseBold
								/></el-icon>
							</div>
							<el-icon
								class="cursor-pointer"
								size="30"
								color="#CFD3DC"
								@click="addMaterial"
								><CirclePlus
							/></el-icon>
							<input
								type="file"
								multiple
								accept="image/*"
								ref="fileInput"
								style="display: none"
								@change="handleFileChange"
							/>
						</div>
						<el-row>
							<div>置顶</div>
							<cl-flex1 />
							<el-button type="primary" link @click="materialSel = !materialSel">
								{{ materialSel ? "完成" : "编辑" }}
							</el-button>
						</el-row>

						<div class="material-digest">
							<div class="item" v-for="(obj, index) in materials" :key="index">
								<img :src="domain + obj.fullpath" @click="copyClick(obj)" />
								<el-icon
									v-show="materialSel"
									class="icon"
									size="15"
									@click="switchDigest(obj, 0)"
									><CloseBold
								/></el-icon>
							</div>
						</div>

						<div class="material-all">
							<div class="item" v-for="(obj, index) in materialAlls" :key="index">
								<img :src="domain + obj.fullpath" @click="copyClick(obj)" />
								<el-icon
									v-show="materialSel"
									class="icon"
									size="15"
									@click="switchDigest(obj, 1)"
									><Plus
								/></el-icon>
							</div>
						</div>
					</el-dialog>
				</el-form-item>

				<el-form-item label="活动标题" :required="true" prop="subject">
					<el-input v-model="form.subject" placeholder="请输入活动标题" />
				</el-form-item>

				<el-form-item label="活动内容" :required="true" prop="content">
					<el-input
						type="textarea"
						v-model="form.content"
						placeholder="请输入活动内容"
						rows="6"
					/>
				</el-form-item>

				<div v-for="(location, index) in form.locations" :key="index" class="locationWrap">
					<div class="location-header" v-if="form.locations.length > 1">
						<el-icon
							class="cursor-pointer delete-icon"
							size="20"
							color="crimson"
							@click="removeLocation(index)"
							><Delete
						/></el-icon>
					</div>
					<el-form-item label="地点" :required="true">
						<el-row :gutter="20">
							<el-col :span="8" class="location">
								<el-select
									v-model="location.country_id"
									placeholder="请选择国家"
									@change="(value) => handleCountryChange(value, index)"
								>
									<el-option
										v-for="country in countries"
										:key="country.id"
										:label="country.name"
										:value="country.id"
									/>
								</el-select>
							</el-col>
							<el-col :span="8" class="location">
								<el-select
									v-model="location.province_id"
									placeholder="请选择地区"
									@change="(value) => handleRegionChange(value, index)"
									:disabled="regions.length === 0"
								>
									<el-option
										v-for="region in regions"
										:key="region.id"
										:label="region.name"
										:value="region.id"
									/>
								</el-select>
							</el-col>
							<el-col :span="8" class="location">
								<el-select
									v-model="location.city_id"
									placeholder="请选择城市"
									:disabled="cities.length === 0"
								>
									<el-option
										v-for="city in cities"
										:key="city.id"
										:label="city.name"
										:value="city.id"
									/>
								</el-select>
							</el-col>
						</el-row>
						<el-row>
							<el-checkbox
								class="app"
								v-model="location.app_join_event"
								label="app 参加"
								true-value="1"
								false-value="0"
							/>
						</el-row>
					</el-form-item>

					<el-form-item>
						<div class="hotspot">
							<span>常用:</span
							><el-link
								type="primary"
								v-for="obj in hotspot"
								:key="obj.id"
								@click="hotspotClick(obj, index)"
								>{{ obj.name }}</el-link
							>
						</div></el-form-item
					>

					<el-form-item label="专业" :required="true">
						<el-checkbox-group v-model="location.school_major">
							<el-checkbox
								v-for="obj in school_major"
								:key="obj.id"
								:label="obj.short"
								:value="obj.id"
							/>
						</el-checkbox-group>
					</el-form-item>

					<el-form-item label="活动类型" :required="true">
						<el-checkbox-group v-model="location.event_type">
							<el-checkbox
								v-for="obj in event_type"
								:key="obj.id"
								:label="obj.name"
								:value="obj.id"
							/>
						</el-checkbox-group>
					</el-form-item>

					<el-form-item label="时间">
						<el-date-picker
							v-model="location.begin_date"
							type="datetime"
							placeholder="请选择开始时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
							@change="(value) => handleDateChange(value, location)"
						/>
						<span class="split">-</span>
						<el-date-picker
							v-model="location.end_date"
							type="datetime"
							placeholder="请选择结束时间"
							format="YYYY-MM-DD HH:mm"
							value-format="X"
						/>
					</el-form-item>
				</div>

				<el-form-item>
					<el-icon class="cursor-pointer" size="30" color="#CFD3DC" @click="addLocation"
						><CirclePlus
					/></el-icon>
				</el-form-item>

				<el-form-item style="margin-top: 40px">
					<el-button type="primary" @click="handleSubmit(formRef)">提交</el-button>
					<el-button @click="router.back()">取消</el-button>
				</el-form-item>
			</el-form>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { CirclePlus, Delete, Plus, CloseBold } from "@element-plus/icons-vue";
import type { FormInstance } from "element-plus";
import { ElMessage } from "element-plus";
import { useClipboard } from "@vueuse/core";
import { useCool } from "/@/cool";
import { hotspot } from "../data/index";
import { isDev } from "/@/config";
import _ from "lodash";

const { copy } = useClipboard();
const { service, router } = useCool();

const domain = isDev ? "http://127.0.0.1:8080" : "https://static.chasedream.com";

const form = reactive({
	school_id: "",
	subject: "",
	content: "",
	locations: [
		{
			country_id: "",
			province_id: "",
			city_id: "",
			app_join_event: 0,
			school_major: [],
			event_type: [],
			begin_date: "",
			end_date: ""
		}
	]
});

const rules = reactive({
	school_id: [{ required: true, message: "请选择学校", trigger: "change" }],
	subject: [{ required: true, message: "请输入活动标题", trigger: "blur" }],
	content: [{ required: true, message: "请输入活动内容", trigger: "blur" }]
});

const formRef = ref<FormInstance>();
const fileInput = ref(null);
const school_major = ref([]);
const event_type = ref([]);
const organizations = ref([]);
const materialNews = ref([]);
const materials = ref([]);
const materialAlls = ref([]);
const countries = ref([]);
const regions = ref([]);
const cities = ref([]);
const dialogVisible = ref(false);
const materialSel = ref(false);

const handleSubmit = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	let valid = true;
	let fieldsError = [];

	await formEl.validate((isValid, fields) => {
		if (!isValid) return;

		form.locations.forEach((location, index) => {
			if (location.country_id == "" || location.province_id == "") {
				valid = false;
				fieldsError.push({ index, field: "country_id", message: "请选择地点" });
			}
			if (location.school_major.length === 0) {
				valid = false;
				fieldsError.push({ index, field: "school_major", message: "请选择至少一个专业" });
			}
			if (location.event_type.length === 0) {
				valid = false;
				fieldsError.push({ index, field: "event_type", message: "请选择至少一种活动类型" });
			}
		});

		if (!valid) {
			fieldsError.forEach((error) => {
				ElMessage.error(`${error.message}`);
			});
			return;
		}

		service.base.common.event
			.create({
				...form
			})
			.then((res) => {
				ElMessage({
					message: "已添加!",
					type: "success"
				});
				router.back();
			})
			.catch((err) => {
				ElMessage.error(err.message);
			});
	});
};

const handleDateChange = (value, location) => {
	if (value) {
		location.end_date = parseInt(value) + 60 * 60;
	}
};

const addMaterial = () => {
	fileInput.value?.click();
};

const handleFileChange = async (event: any) => {
	const files = event.target.files;
	if (files) {
		await upload(files);
	}

	event.target.value = "";
};

const upload = async (files: any) => {
	for (let i = 0; i < files.length; i++) {
		const formData = new FormData();
		formData.append("files", files[i]);

		try {
			const baseUrl = isDev
				? "http://localhost:9000/dev/admin/base/open/upload4Event"
				: "https://connect.chasedream.com/api/v2/admin/base/open/upload4Event";

			const params = new URLSearchParams({
				school_id: form.school_id,
				type: "core"
			});

			const url = `${baseUrl}?${params.toString()}`;

			const response = await fetch(url, {
				method: "POST",
				body: formData
			});

			if (response.ok) {
				const res = await response.json();
				materialNews.value.push(res.data);
			} else {
				ElMessage.error("Upload failed");
			}
		} catch (err: any) {
			ElMessage.error(err.message);
		}
	}
};

const copyClick = (obj) => {
	copy(`${domain}${obj.fullpath}`);

	ElMessage({
		message: "已复制",
		type: "success"
	});
};

const delMaterial = async (obj, index) => {
	await service.base.common.event.imageDelete({
		id: obj.id
	});

	materialNews.value.splice(index, 1);
};

const switchDigest = async (obj, digest) => {
	await service.base.common.event.imageDigest({
		id: obj.id,
		school_id: form.school_id,
		digest
	});

	service.base.common.event
		.materialDigest({
			school_id: form.school_id,
			digest: 1
		})
		.then((res) => {
			materials.value = res[0];
		});

	service.base.common.event
		.materialDigest({
			school_id: form.school_id
		})
		.then((res) => {
			materialAlls.value = res[0];
		});
};

const materialClick = () => {
	if (form.school_id.length === 0) {
		ElMessage.error("请选学校!");
		return;
	}
	dialogVisible.value = true;

	service.base.common.event
		.materialDigest({
			school_id: form.school_id
		})
		.then((res) => {
			materialAlls.value = res[0];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const orgChange = (val) => {
	service.base.common.event
		.school_major({
			school_id: val
		})
		.then((res) => {
			school_major.value = res;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});

	service.base.common.event
		.materialDigest({
			school_id: val,
			digest: 1
		})
		.then((res) => {
			materials.value = res[0];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const hotspotClick = async (obj, index) => {
	await loadGeo(obj.country_id, "regions");
	await loadGeo(obj.province_id, "cities");

	form.locations[index].country_id = obj.country_id;
	form.locations[index].province_id = obj.province_id;
	form.locations[index].city_id = obj.city_id;
};

const loadEventTypes = () => {
	service.base.common.event
		.type({})
		.then((res) => {
			event_type.value = res;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const loadOrgs = () => {
	service.base.common.event
		.orgs({})
		.then((res) => {
			organizations.value = res;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const loadGeo = (parent_id = "", target = "") => {
	service.base.common.event
		.geo({ parent_id })
		.then((res) => {
			if (target === "countries") {
				countries.value = res;
			} else if (target === "regions") {
				regions.value = res;
			} else if (target === "cities") {
				cities.value = res;
			}
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const handleCountryChange = (country_id, index) => {
	console.log(index, country_id);
	form.locations[index].province_id = "";
	form.locations[index].city_id = "";
	regions.value = [];
	cities.value = [];
	loadGeo(country_id, "regions");
};

const handleRegionChange = (province_id, index) => {
	form.locations[index].city_id = "";
	cities.value = [];
	loadGeo(province_id, "cities");
};

const addLocation = () => {
	form.locations.push({
		country_id: "",
		province_id: "",
		city_id: "",
		app_join_event: 0,
		school_major: [],
		event_type: [],
		begin_date: "",
		end_date: ""
	});
};

const removeLocation = (index) => {
	form.locations.splice(index, 1);
};

onMounted(() => {
	loadOrgs();
	loadEventTypes();
	loadGeo("", "countries");
});
</script>

<style lang="scss" scoped>
@import "../css/add.scss";
</style>
