const colors = [
	{
		val: 0,
		color: "#000",
		title: "黑"
	},
	{
		val: 1,
		color: "#ff0101",
		title: "红"
	},
	{
		val: 2,
		color: "#2897c5",
		title: "蓝"
	},
	{
		val: 3,
		color: "orange",
		title: "橙"
	},
	{
		val: 4,
		color: "#9f00fb",
		title: "紫"
	},
	{
		val: 5,
		color: "#1ab394",
		title: "绿"
	},
	{
		val: 6,
		color: "red",
		title: "红字黄底"
	},
	{
		val: 7,
		color: "#fcf301",
		title: "黄字红底"
	},
	{
		val: 8,
		color: "#ff0101",
		title: "红字灰底"
	}
];

const stick = [
	{
		val: 0,
		text: "无"
	},
	{
		val: 1,
		text: "本版置顶"
	},
	{
		val: 2,
		text: "分类置顶"
	},
	{
		val: 3,
		text: "全局置顶"
	},
	{
		val: 4,
		text: "活动置顶"
	}
];

const hotspot = [
	{
		name: "在线",
		country_id: 4134,
		province_id: 4135,
		city_id: ""
	},
	{
		name: "北京",
		country_id: 1,
		province_id: 2,
		city_id: ""
	},
	{
		name: "上海",
		country_id: 1,
		province_id: 111,
		city_id: ""
	},
	{
		name: "深圳",
		country_id: 1,
		province_id: 265,
		city_id: 268
	},
	{
		name: "广州",
		country_id: 1,
		province_id: 265,
		city_id: 266
	},
	{
		name: "南京",
		country_id: 1,
		province_id: 130,
		city_id: 131
	},
	{
		name: "香港",
		country_id: 1,
		province_id: 503,
		city_id: ""
	},
	{
		name: "天津",
		country_id: 1,
		province_id: 19,
		city_id: ""
	}
];

const forumAccount = [
	{
		uid: 99999,
		username: "CAN"
	},
	{
		uid: 1404667,
		username: "妥妥"
	},
	{
		uid: 923293,
		username: "QS"
	},
	{
		uid: 1100000,
		username: "硕士申请服务"
	},
	{
		uid: 1433,
		username: "steven"
	},
	{
		uid: 637,
		username: "ChaseDream"
	}
];

const wwwAccount = [
	{
		uid: 637,
		username: "ChaseDream"
	}
];

const push3Color = ["red", "green", "blue", "black"];

const push2WeekHot = [
	{ value: "0", label: "0" },
	{ value: "1", label: "1" },
	{ value: "2", label: "2" },
	{ value: "3", label: "3" },
	{ value: "4", label: "4" },
	{ value: "5", label: "5" },
	{ value: "6", label: "6" },
	{ value: "7", label: "7" },
	{ value: "8", label: "8" },
	{ value: "9", label: "9" },
	{ value: "10", label: "10" }
];

export { colors, stick, hotspot, forumAccount, wwwAccount, push3Color, push2WeekHot };
