.wrapper {
    margin: 10px 5px 10px 5px;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    .locationWrap {
        padding: 20px 0 10px 0;
        margin-bottom: 20px;
        border: 1px solid #efefef;
    }
    .location {
        .el-select {
            width: 200px;
            margin-right: 5px;
        }
    } 
    .app {
        margin-left: 20px;
    }
    .hotspot {
        span {
            margin-right: 10px;
        }
        a {
            margin-right: 10px;
        }
    }
    .el-checkbox {
        margin-right: 5px;
    }
    .form-container {
        overflow-y: auto;
        padding-right: 16px; 
        .material {
            padding-right: 5px;
            position: relative;
            .copy {
                width: 45px;
                height: 20px;
                position: absolute;
                bottom: 10px;
                z-index: 999;
                color: #fff;
                line-height: 20px;
                text-align: center;
                font-size: 12px;
                background-color: lightgray;
                opacity: 0.9;
            }
        }
        .material-new {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 5px;
            .item {
                width: 45px; 
                height: 45px; 
                margin: 5px;
                position: relative;
                img {
                    width: 45px; 
                    height: 45px; 
                }
                .icon {
                    position: absolute;
                    top: 0;
                    right: 0;
                    cursor: pointer;
                    color: greenyellow;
                }
            }
        }
        .material-digest {
            display: flex;
            flex-wrap: wrap;
            height: 130px;
            background: #f8f8f9;
            .item {
                width: 45px; 
                height: 45px; 
                margin: 5px;
                position: relative;
                img {
                    width: 45px; 
                    height: 45px; 
                }
                .icon {
                    position: absolute;
                    top: 0;
                    left: 0;
                    cursor: pointer;
                    color: red;
                }
            }
        }
        .material-all {
            display: flex;
            flex-wrap: wrap;
            margin: 30px 0;
            .item {
                width: 45px; 
                height: 45px; 
                margin: 5px;
                position: relative;
                img {
                    width: 45px; 
                    height: 45px; 
                }
                .icon {
                    position: absolute;
                    top: 0;
                    left: 0;
                    cursor: pointer;
                    color: greenyellow;
                }
            }
        }
    }
    .location-header {
        position: relative;
    }
    .split {
        margin: 0 5px;
    }
    .delete-icon {
        position: absolute;
        right: 10px;
        top: 0;
        z-index: 999;
    }
}

.cursor-pointer {
	cursor: pointer;
}