html.dark {
	--el-bg-color: #2c3142;

	.cl-dialog {
		.el-dialog__header,
		.cl-form-tabs {
			border-bottom: 1px solid #222;
		}

		.cl-form-tabs * {
			border-color: #222;
		}
	}

	.cl-crud {
		background-color: transparent !important;

		.el-table {
			&__header {
				tr {
					th {
						background-color: #222222 !important;
						color: #fff;
					}
				}
			}

			&__body {
				tr {
					&.current-row {
						td {
							background-color: #18222c !important;
						}
					}
				}
			}
		}
	}

	.app-layout {
		background-color: var(--el-bg-color) !important;

		.a-menu {
			.el-menu-item {
				border-radius: 6px !important;
			}
		}

		.app-topbar {
			background-color: transparent;
			color: #fff;

			span {
				color: #fff;
			}

			.el-breadcrumb {
				span {
					color: #ddd;
				}

				&__item {
					&:last-child {
						span {
							color: #fff;
						}
					}
				}
			}
		}

		.app-views {
			background-color: #2f3447;
		}
	}

	.el-overlay {
		background-size: 4px 4px;
		backdrop-filter: saturate(50%) blur(4px);
	}
}

body.theme {
	&-jihei {
		.app-layout {
			background-color: rgba(47, 52, 71, 0.9);

			.a-menu {
				.el-menu-item {
					border-radius: 6px !important;
				}
			}

			.app-topbar {
				background-color: transparent;
				color: #fff;

				span {
					color: #fff;
				}

				.el-breadcrumb {
					span {
						color: #ddd;
					}

					&__item {
						&:last-child {
							span {
								color: #fff;
							}
						}
					}
				}
			}
		}
	}

	&-guolv {
		.app-layout {
			.app-slider__logo {
				background-color: var(--color-primary);
			}
		}
	}

	&-jiangzi {
		.app-layout {
			.app-slider {
				background-color: var(--color-primary);

				.el-sub-menu__title,
				.el-menu-item {
					color: #fff;
					border-radius: 10px;
					position: relative;

					&::after {
						position: absolute;
						left: 10px;
						top: 0;
						content: "";
						display: block;
						height: 100%;
						width: calc(100% - 20px);
						border-radius: 8px;
					}

					&:hover {
						&::after {
							background-color: rgba(255, 255, 255, 0.2);
						}
					}

					&.is-active {
						&::after {
							background-color: #fff;
						}

						.cl-svg,
						span {
							color: var(--color-primary);
							position: relative;
							z-index: 2;
						}
					}
				}
			}
		}
	}

	&-cangqing {
		.app-layout {
			.app-slider {
				background: linear-gradient(30deg, #7397ab, #75878a);

				.el-sub-menu__title,
				.el-menu-item {
					color: #fff;

					&:hover {
						background-color: rgba(255, 255, 255, 0.2) !important;
					}

					&.is-active {
						background-color: #fff !important;

						.cl-svg,
						span {
							color: var(--color-primary);
						}
					}
				}
			}
		}
	}
}
