<template>
	<div class="wrapper">
		<el-row class="opt">
			<el-col :span="12">
				<el-button @click="router.push('/console/portal/add')" type="primary"
					>添加</el-button
				>
			</el-col>
			<el-col :span="12" style="text-align: right; display: flex; align-items: center; justify-content: flex-end; gap: 10px;">
				<span>Banner位置</span>
				<el-input-number
					v-model="bannerIndex"
					:min="0"
					:max="999999"
					:precision="0"
					style="width: 120px"
					@change="updateBannerIndex"
				/>
				<el-input
					placeholder="请输入标题"
					v-model="search.s"
					style="width: 300px"
					clearable
					@clear="refresh"
				>
					<template #append>
						<el-button :icon="Search" @click="refresh" />
					</template>
				</el-input>
			</el-col>
		</el-row>
		<el-row class="list">
			<el-col :span="24">
				<el-table
					border
					style="width: 100%"
					:data="tableData"
					:max-height="tableHeight"
					:row-style="{ height: '40px' }"
					:header-cell-style="{ background: '#ebeef5', color: '#333' }"
				>
					<el-table-column fixed prop="id" label="ID" width="70" align="center">
					</el-table-column>
					<el-table-column label="标题" align="left">
						<template #default="scope">
							{{ scope.row.title }}
						</template>
					</el-table-column>
					<el-table-column width="100" label="类型" align="center">
						<template #default="scope">
							{{ scope.row.type === 0 ? '标题摘要' : scope.row.type === 1 ? '通栏图片' : '-' }}
						</template>
					</el-table-column>
					<el-table-column width="80" label="排序" align="center">
						<template #default="scope">
							<el-select
								v-model="scope.row.displayOrder"
								@change="updateDisplayOrder(scope.row)"
								size="small"								
							>
								<el-option :value="3" label="3" />
								<el-option :value="2" label="2" />
								<el-option :value="1" label="1" />
								<el-option :value="0" label="0" />
							</el-select>
						</template>
					</el-table-column>
					<el-table-column width="110" label="摘要" align="center" prop="summary" :show-overflow-tooltip="true">
					</el-table-column>
					<el-table-column width="70" label="帖子" align="center">
						<template #default="scope">
							<el-link v-if="scope.row.forumTid > 0" @click="viewThread(scope.row)" type="primary" target="_blank">查看</el-link>							
						</template>
					</el-table-column>
					<el-table-column width="110" label="作者/点击数" align="center">
						<template #default="scope">
							{{ scope.row.author }} <br/>
							{{ scope.row.hits }} 
						</template>
					</el-table-column>
					<el-table-column width="110" label="创建时间" align="center">
						<template #default="scope">
							{{ formatter(scope.row.datetime) }} 
						</template>
					</el-table-column>
					<el-table-column fixed="right" label="操作" width="100" align="center">
						<template #default="scope">
							<el-icon
								@click="editClick(scope.row)"
								class="cursor-pointer"
								style="color: #464bd7; margin-right: 8px"
								:size="20"
								><edit
							/></el-icon>

							<el-popconfirm
										title="确定要删除吗?"
										@confirm="deleteHandler(scope.row)"
									>
										<template #reference>
											<el-icon
												class="cursor-pointer"
												style="color: crimson; margin-right: 8px"
												:size="20"
												><delete
											/></el-icon>
										</template>
									</el-popconfirm>
						</template>
					</el-table-column>
				</el-table>
			</el-col>
		</el-row>
		<el-row class="page">
			<el-pagination
				class="pagination"
				background
				:currentPage="currentPage"
				:page-size="pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</el-row>
	</div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import { Edit, Delete, Search } from "@element-plus/icons-vue";
import { ref, reactive, onMounted, nextTick } from "vue";
import { useCool } from "/@/cool";
import { removeEmptyFromObject } from "/@/cool/utils";
import { datelineToDate } from "/@/cool/utils";

const { service, router } = useCool();

const search = reactive({
	id: "",
	s: ""
});

const bannerIndex = ref(0);
const tableHeight = ref(0);
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(50);
const total = ref(0);

const formatter = (datetime: any) => {
	return datelineToDate(datetime, "YYYY-MM-DD HH:mm:ss");
};

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};

const editClick = (row: any) => {
	router.push(`/console/portal/edit?id=${row.id}`);
};

const viewThread = (row: any) => {
	window.open(
		`https://forum.chasedream.com/thread-${row.forumTid}-1-1.html`,
		"_blank"
	);
};

const deleteHandler = (row: any) => {
	service.base.common.portal
		.portalDelete({
			id: row.id
		})
		.then(() => {
			ElMessage({
				message: "已删除!",
				type: "success"
			});
			refresh();
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.portal
		.portalPage({
			...s,
			page: currentPage.value,
			pageSize: pageSize.value
		})
		.then((res) => {
			tableData.value = res[0];
			total.value = res[1];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

// 获取 Banner 索引
const getBannerIndex = () => {
	service.base.common.portal
		.getPortalBannerIndex()
		.then((res) => {
			bannerIndex.value = res || 0;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

// 更新 Banner 索引
const updateBannerIndex = (value: number) => {
	service.base.common.portal
		.setPortalBannerIndex({
			value
		})
		.then(() => {
			ElMessage({
				message: "Banner索引已更新!",
				type: "success"
			});
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

// 更新排序
const updateDisplayOrder = (row: any) => {
	service.base.common.portal
		.updateDisplayOrder({
			id: row.id,
			displayOrder: row.displayOrder
		})
		.then(() => {
			ElMessage({
				message: "排序已更新!",
				type: "success"
			});
		})
		.catch((err) => {
			ElMessage.error(err.message);
			// 如果更新失败，恢复原值
			refresh();
		});
};


refresh();
getBannerIndex();

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 250;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 250;
		};
	});
});
</script>

<style lang="scss" scoped>
@import "../css/index.scss";
</style>
