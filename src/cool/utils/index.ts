import { isArray, isNumber, isString, isEmpty, orderBy } from "lodash-es";
import { resolveComponent } from "vue";
import storage from "./storage";
import dayjs from "dayjs";
import isLeapYear from "dayjs/plugin/isLeapYear";
import "dayjs/locale/zh-cn";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import _ from "lodash";

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isLeapYear);

dayjs.locale("zh-cn");

export function isValueEmpty(value) {
	// 检查 null 和 undefined
	if (value == null) {
		return true;
	}
	// 检查数字
	if (typeof value === "number") {
		return false;
	}
	// 使用 lodash 的 isEmpty 方法检查其他类型
	return _.isEmpty(value);
}

// 首字母大写
export function firstUpperCase(value: string): string {
	return value.replace(/\b(\w)(\w*)/g, function ($0, $1, $2) {
		return $1.toUpperCase() + $2;
	});
}

// 获取方法名
export function getNames(value: any) {
	return Object.getOwnPropertyNames(value.constructor.prototype);
}

// 获取地址栏参数
export function getUrlParam(name: string): string | null {
	const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
	const r = window.location.search.substr(1).match(reg);
	if (r != null) return decodeURIComponent(r[2]);
	return null;
}

// 文件名
export function filename(path: string): string {
	return basename(path.substring(0, path.lastIndexOf(".")));
}

// 路径名称
export function basename(path: string): string {
	let index = path.lastIndexOf("/");
	index = index > -1 ? index : path.lastIndexOf("\\");
	if (index < 0) {
		return path;
	}
	return path.substring(index + 1);
}

// 文件扩展名
export function extname(path: string): string {
	return path.substring(path.lastIndexOf(".") + 1).split(/(\?|&)/)[0];
}

// 横杠转驼峰
export function toCamel(str: string): string {
	return str.replace(/([^-])(?:-+([^-]))/g, function ($0, $1, $2) {
		return $1 + $2.toUpperCase();
	});
}

// uuid
export function uuid(separator = "-"): string {
	const s: any[] = [];
	const hexDigits = "0123456789abcdef";
	for (let i = 0; i < 36; i++) {
		s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
	}
	s[14] = "4";
	s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1);
	s[8] = s[13] = s[18] = s[23] = separator;

	return s.join("");
}

// 浏览器信息
export function getBrowser() {
	const { clientHeight, clientWidth } = document.documentElement;

	// 浏览器信息
	const ua = navigator.userAgent.toLowerCase();

	// 浏览器类型
	let type = (ua.match(/firefox|chrome|safari|opera/g) || "other")[0];

	if ((ua.match(/msie|trident/g) || [])[0]) {
		type = "msie";
	}

	// 平台标签
	let tag = "";

	const isTocuh =
		"ontouchstart" in window || ua.indexOf("touch") !== -1 || ua.indexOf("mobile") !== -1;
	if (isTocuh) {
		if (ua.indexOf("ipad") !== -1) {
			tag = "pad";
		} else if (ua.indexOf("mobile") !== -1) {
			tag = "mobile";
		} else if (ua.indexOf("android") !== -1) {
			tag = "androidPad";
		} else {
			tag = "pc";
		}
	} else {
		tag = "pc";
	}

	// 浏览器内核
	let prefix = "";

	switch (type) {
		case "chrome":
		case "safari":
		case "mobile":
			prefix = "webkit";
			break;
		case "msie":
			prefix = "ms";
			break;
		case "firefox":
			prefix = "Moz";
			break;
		case "opera":
			prefix = "O";
			break;
		default:
			prefix = "webkit";
			break;
	}

	// 操作平台
	const plat = ua.indexOf("android") > 0 ? "android" : navigator.platform.toLowerCase();

	// 屏幕信息
	let screen = "full";

	if (clientWidth < 768) {
		screen = "xs";
	} else if (clientWidth < 992) {
		screen = "sm";
	} else if (clientWidth < 1200) {
		screen = "md";
	} else if (clientWidth < 1920) {
		screen = "xl";
	} else {
		screen = "full";
	}

	// 是否 ios
	const isIOS = !!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);

	// 浏览器版本
	const version = (ua.match(/[\s\S]+(?:rv|it|ra|ie)[\/: ]([\d.]+)/) || [])[1];

	// 是否 PC 端
	const isPC = tag === "pc";

	// 是否移动端
	const isMobile = isPC ? false : true;

	// 是否移动端 + 屏幕宽过小
	const isMini = screen === "xs" || isMobile;

	return {
		height: clientHeight,
		width: clientWidth,
		version,
		type,
		plat,
		tag,
		prefix,
		isMobile,
		isIOS,
		isPC,
		isMini,
		screen
	};
}

// 路径转数组
export function deepPaths(paths: string[], splitor?: string) {
	const list: any[] = [];

	paths.forEach((e) => {
		const arr: string[] = e.split(splitor || "/").filter(Boolean);

		let c = list;

		arr.forEach((a, i) => {
			let d = c.find((e) => e.label == a);

			if (!d) {
				d = {
					label: a,
					value: a,
					children: arr[i + 1] ? [] : null
				};

				c.push(d);
			}

			if (d.children) {
				c = d.children;
			}
		});
	});

	return list;
}

// 列表转树形
export function deepTree(list: any[], sort?: "desc" | "asc"): any[] {
	const newList: any[] = [];
	const map: any = {};

	orderBy(list, "orderNum", sort)
		.map((e) => {
			map[e.id] = e;
			return e;
		})
		.forEach((e) => {
			const parent = map[e.parentId];

			if (parent) {
				(parent.children || (parent.children = [])).push(e);
			} else {
				newList.push(e);
			}
		});

	return newList;
}

// 树形转列表
export function revDeepTree(list: any[]) {
	const arr: any[] = [];
	let id = 0;

	function deep(list: any[], parentId: number) {
		list.forEach((e) => {
			if (!e.id) {
				e.id = ++id;
			}

			if (!e.parentId) {
				e.parentId = parentId;
			}

			arr.push(e);

			if (e.children && isArray(e.children) && e.id) {
				deep(e.children, e.id);
			}
		});
	}

	deep(list || [], 0);

	return arr;
}

// 路径转对象
export function path2Obj(list: any[]) {
	const data: any = {};

	list.forEach(({ path, value }) => {
		if (path) {
			const arr: string[] = path.split("/");
			const parents = arr.slice(0, arr.length - 1);
			const name = basename(path).replace(".ts", "");

			let curr = data;

			parents.forEach((k) => {
				if (!curr[k]) {
					curr[k] = {};
				}

				curr = curr[k];
			});

			curr[name] = value;
		}
	});

	return data;
}

// 是否是组件
export function isComponent(name: string) {
	return !isString(resolveComponent(name));
}

// 是否Promise
export function isPromise(val: any) {
	return val && Object.prototype.toString.call(val) === "[object Promise]";
}

// 单位转换
export function parsePx(val: string | number) {
	return isNumber(val) ? `${val}px` : val;
}

// 延迟
export function sleep(duration: number) {
	return new Promise((resolve) => {
		setTimeout(() => {
			resolve(true);
		}, duration);
	});
}

export function highlight(keywords: string, text: string) {
	const options = {
		startTag: "<b class='highlight'>",
		endTag: "</b>"
	};

	const wrapper = (match: string) => {
		return options.startTag + match + options.endTag;
	};

	let arr = keywords.split(" ");
	arr = arr.filter(function (char) {
		return char !== "";
	});

	let regex = arr.join("|");
	regex = regex.replace(/[-[\]{}()*+?.,\\^$]/g, "\\$&");
	const matcher = new RegExp(regex, "gi");

	return text.replace(matcher, wrapper);
}

export function datelineToDate(dateline: number, format = "YYYY-MM-DD HH:mm:ss") {
	return dayjs.unix(dateline).tz("Asia/Shanghai").format(format);
}

export function utcToDate(utc: number, format = "YYYY-MM-DD HH:mm:ss") {
	const timestamp = (utc - 621355968000000000) / 10000000;
	return dayjs.unix(timestamp).tz("Asia/Shanghai").format(format);
}

export function dateToTimeStamp(date: string) {
	return date == "0" ? 0 : dayjs(date).tz("Asia/Shanghai").unix();
}

export function today(format = "YYYY-MM-DD HH:mm:ss") {
	return dayjs().format(format);
}

export function datelineFormat(timestamp: number) {
	const now = dayjs();
	const target = dayjs.unix(timestamp);

	if (now.isSame(target, "day")) {
		return target.format("HH:mm"); // 当天：输出小时、分
	} else if (now.isSame(target, "week")) {
		return target.format("dddd"); // 本周内：输出是星期几
	} else if (now.isSame(target, "year")) {
		return target.format("MM-DD"); // 当年内：输出月日
	} else {
		return target.format("YYYY-MM-DD"); // 本年外：输出年月日
	}
}

export function removeEmptyFromObject(obj: object) {
	return Object.fromEntries(Object.entries(obj).filter(([, v]) => !isEmpty(v)));
}

export { storage };
export * from "./loading";
