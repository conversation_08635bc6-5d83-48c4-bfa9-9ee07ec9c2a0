# 介绍

**cool-admin for vue**是基于[Vue.js](https://v3.cn.vuejs.org)开发的，[官方文档](https://v3.cn.vuejs.org)。

Vue.js 是一套用于构建用户界面的渐进式框架。与其它大型框架不同的是，Vue 被设计为可以自底向上逐层应用。

尝试 `cool-admin` 最简单的方法就是查看文档及运行示例。

<img src='https://cool-js.com/assets/login.350e25ec.png' />

<img src='https://cool-js.com/assets/home.1706ac70.png' />

<span style="font-size: 18px; color: #F56C6C">v7.0.0 新增 Ai 极速编码 ~~~~</span>

<img src='https://cool-js.com/assets/ai-code2.9a122008.png' />

## 代码仓库

**cool-admin for vue** 是开源免费的，遵循[MIT](https://baike.baidu.com/item/MIT/10772952)开源协议，意味着您无需支付任何费用，也无需授权，即可将它应用到您的产品中。

开源免费，并不意味着您可以将 cool-admin 应用到非法的领域，比如涉及赌博，暴力等方面。如因此产生纠纷等法律问题，`cool-admin`不承担任何责任。

[https://github.com/cool-team-official/cool-admin-vue](https://github.com/cool-team-official/cool-admin-vue)

```shell
git clone https://github.com/cool-team-official/cool-admin-vue.git
```

## 技术选型

-   [Vue.js](https://v3.cn.vuejs.org)，基础框架；
-   [VueRouter](https://router.vuejs.org)，Vue.js 官方路由；
-   [Pinia](https://pinia.vuejs.org)，轻量级状态管理库；
-   [ElementPlus](https://element-plus.gitee.io/zh-CN)，桌面端组件库；
-   [Vite](https://vitejs.cn)，构建工具；
