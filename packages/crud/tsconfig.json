{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "noImplicitAny": false, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": false, "declaration": true, "declarationDir": "types", "inlineSourceMap": false, "disableSizeLimit": true, "baseUrl": ".", "outDir": "dist", "types": ["webpack-env"], "paths": {}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "src/demo/*", "src/main.ts", "src/components/*"]}