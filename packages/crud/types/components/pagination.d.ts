declare const _default: import("vue").DefineComponent<{}, {
    total: import("vue").Ref<number>;
    currentPage: import("vue").Ref<number>;
    pageSize: import("vue").Ref<number>;
    onCurrentChange: (index: number) => void;
    onSizeChange: (size: number) => void;
    setPagination: (res: any) => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}>;
export default _default;
