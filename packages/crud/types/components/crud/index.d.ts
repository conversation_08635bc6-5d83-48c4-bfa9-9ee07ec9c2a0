declare const _default: import("vue").DefineComponent<{
    name: StringConstructor;
    border: BooleanConstructor;
    padding: {
        type: StringConstructor;
        default: string;
    };
}, () => any, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    name: StringConstructor;
    border: BooleanConstructor;
    padding: {
        type: StringConstructor;
        default: string;
    };
}>>, {
    border: boolean;
    padding: string;
}, {}>;
export default _default;
