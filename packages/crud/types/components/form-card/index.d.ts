declare const _default: import("vue").DefineComponent<{
    label: StringConstructor;
    expand: {
        type: BooleanConstructor;
        default: boolean;
    };
    isExpand: {
        type: BooleanConstructor;
        default: boolean;
    };
}, () => any, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    label: StringConstructor;
    expand: {
        type: BooleanConstructor;
        default: boolean;
    };
    isExpand: {
        type: BooleanConstructor;
        default: boolean;
    };
}>>, {
    expand: boolean;
    isExpand: boolean;
}, {}>;
export default _default;
