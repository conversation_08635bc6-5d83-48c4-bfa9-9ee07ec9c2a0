declare const _default: import("vue").DefineComponent<{
    inner: BooleanConstructor;
    inline: BooleanConstructor;
    enablePlugin: {
        type: BooleanConstructor;
        default: boolean;
    };
}, () => any, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    inner: BooleanConstructor;
    inline: BooleanConstructor;
    enablePlugin: {
        type: BooleanConstructor;
        default: boolean;
    };
}>>, {
    inline: boolean;
    inner: boolean;
    enablePlugin: boolean;
}, {}>;
export default _default;
