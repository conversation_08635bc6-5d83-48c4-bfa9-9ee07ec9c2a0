{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "noImplicitAny": false, "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "experimentalDecorators": true, "esModuleInterop": true, "lib": ["esnext", "dom", "WebWorker"], "types": ["vite/client", "element-plus/global"], "paths": {"/@/*": ["./src/*"], "/$/*": ["./src/modules/*"], "/#/*": ["./src/plugins/*"], "/~/*": ["./packages/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "env.d.ts"], "exclude": ["node_modules", "dist"]}